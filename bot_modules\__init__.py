#!/usr/bin/env python3
"""
🤖 BOT MODULES PACKAGE
=====================

Modular components for the Enhanced Trading Bot V5.0
Refactored from main_bot.py for better organization and maintainability.

This package contains:
- core: Core system functionality
- analyzers: Analysis algorithms and processors  
- communication: Telegram and notification systems
- utilities: Helper functions and tools
- admin: Administrative and management systems
"""

__version__ = "5.0"
__author__ = "Enhanced Trading Bot Team"

# Package-level imports for easy access
from .core import *
from .analyzers import *
from .communication import *
from .utilities import *
from .admin import *

__all__ = [
    # Core modules
    'ModuleLoader',
    'SystemHealth',
    'ConfigManager',
    
    # Analyzer modules
    'AnalyzerManager',
    'ConsensusProcessor',
    'SignalValidator',
    
    # Communication modules
    'TelegramManager',
    'NotificationRouter',
    'MessageHandler',
    
    # Utility modules
    'ChartManager',
    'DataProcessor',
    'HelperFunctions',
    
    # Admin modules
    'AdminManager',
    'MemberManager',
    'SystemMonitor'
]

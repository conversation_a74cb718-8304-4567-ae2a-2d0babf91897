#!/usr/bin/env python3
"""
🌊 ENHANCED MONEY FLOW ANALYZER V2.0 - PRODUCTION READY
=======================================================

Advanced Money Flow Analyzer with Machine Learning Integration:
- 🌊 Comprehensive cross-market money flow analysis
- 📊 Advanced sector rotation detection with ML algorithms
- 🔍 Smart money tracking and institutional flow analysis
- 📈 Real-time flow prediction and trend analysis
- 🎯 Intelligent signal generation with confidence scoring
- 🚀 Performance optimized for crypto markets
- 🛡️ Comprehensive error handling and fallback systems

Author: AI Trading Bot Team
Version: 2.0 - Production Ready
License: Proprietary
"""

import pandas as pd
import numpy as np
import time
import requests
import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Union
from collections import defaultdict, deque
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Enhanced module imports
AVAILABLE_MODULES = {}

try:
    from scipy import stats
    from scipy.signal import find_peaks
    AVAILABLE_MODULES['scipy'] = True
    print("✅ scipy imported successfully - Advanced statistical analysis available")
except ImportError:
    AVAILABLE_MODULES['scipy'] = False
    print("⚠️ scipy not available - Using basic statistical methods")

try:
    from sklearn.preprocessing import StandardScaler
    from sklearn.cluster import DBSCAN, KMeans
    from sklearn.decomposition import PCA
    AVAILABLE_MODULES['sklearn'] = True
    print("✅ scikit-learn imported successfully - ML flow analysis available")
except ImportError:
    AVAILABLE_MODULES['sklearn'] = False
    print("⚠️ scikit-learn not available - Using basic flow analysis")

# Enhanced analysis algorithms import
try:
    import ai_model_manager
    import volume_profile_analyzer
    import point_figure_analyzer
    import fourier_analyzer
    import orderbook_analyzer
    import volume_pattern_analyzer
    import volume_spike_detector
    from intelligent_tp_sl_analyzer import IntelligentTPSLAnalyzer
    from dump_detector import UltraEarlyDumpDetector
    import consensus_analyzer
    ALGORITHMS_AVAILABLE = True
    print("✅ All analysis algorithms imported successfully")
except ImportError as e:
    print(f"⚠️ Some algorithms not available: {e}")
    ALGORITHMS_AVAILABLE = False

print(f"🌊 Money Flow Analyzer V2.0 - Available modules: {len([m for m in AVAILABLE_MODULES.values() if m])}/{len(AVAILABLE_MODULES)}")

class MoneyFlowAnalyzer:
    """
    🌊 ENHANCED MONEY FLOW ANALYZER V2.0 - PRODUCTION READY
    =======================================================

    Advanced Money Flow Analyzer with comprehensive features:
    - 🌊 Multi-algorithm cross-market money flow analysis
    - 📊 Advanced sector rotation detection with ML algorithms
    - 🔍 Smart money tracking and institutional flow analysis
    - 📈 Real-time flow prediction and trend analysis
    - 🎯 Intelligent signal generation with confidence scoring
    - 🚀 Performance optimized for high-frequency crypto data
    """

    def __init__(self,
                 tracking_pairs: List[str] = None,
                 flow_threshold: float = 1000000,  # $1M USD
                 correlation_window: int = 24,     # 24 hours
                 sector_analysis: bool = True,
                 enable_ml_analysis: bool = True,
                 enable_smart_money_tracking: bool = True,
                 enable_flow_prediction: bool = True):
        """
        Initialize Enhanced Money Flow Analyzer V2.0.

        Args:
            tracking_pairs: List of trading pairs to track
            flow_threshold: Minimum flow threshold for detection ($1M)
            correlation_window: Correlation analysis window (24 hours)
            sector_analysis: Enable sector rotation analysis
            enable_ml_analysis: Enable ML-based flow analysis
            enable_smart_money_tracking: Enable smart money tracking
            enable_flow_prediction: Enable flow prediction algorithms
        """
        print("🌊 Initializing Enhanced Money Flow Analyzer V2.0...")

        # Core configuration with validation
        self.tracking_pairs = tracking_pairs or [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT',
            'XRPUSDT', 'DOTUSDT', 'LINKUSDT', 'LTCUSDT', 'AVAXUSDT',
            'MATICUSDT', 'ATOMUSDT', 'NEARUSDT', 'FTMUSDT', 'ALGOUSDT',
            'UNIUSDT', 'AAVEUSDT', 'COMPUSDT', 'MKRUSDT', 'SUSHIUSDT'  # Added DeFi tokens
        ]

        self.flow_threshold = max(100000, flow_threshold)  # Min $100K
        self.correlation_window = max(6, min(168, correlation_window))  # 6-168 hours
        self.sector_analysis = sector_analysis

        # Enhanced features
        self.enable_ml_analysis = enable_ml_analysis and AVAILABLE_MODULES.get('sklearn', False)
        self.enable_smart_money_tracking = enable_smart_money_tracking
        self.enable_flow_prediction = enable_flow_prediction

        # Performance tracking
        self.analysis_stats = {
            "total_analyses": 0,
            "successful_analyses": 0,
            "failed_analyses": 0,
            "average_execution_time": 0.0,
            "flow_detection_count": 0,
            "sector_rotation_count": 0,
            "smart_money_events": 0
        }
        
        # Money flow tracking
        self.flow_history = deque(maxlen=1000)
        self.sector_flows = defaultdict(lambda: deque(maxlen=500))
        self.cross_flows = defaultdict(lambda: deque(maxlen=200))
        
        # ✅ DYNAMIC: Initialize with empty sectors - will be populated dynamically
        self.sectors = {}
        self.sector_keywords = {
            'Layer1': ['bitcoin', 'ethereum', 'blockchain', 'layer-1', 'smart-contracts', 'proof-of-stake', 'proof-of-work'],
            'DeFi': ['defi', 'decentralized-finance', 'dex', 'lending', 'yield-farming', 'liquidity', 'automated-market-maker'],
            'Layer2': ['layer-2', 'scaling', 'rollup', 'sidechain', 'polygon', 'arbitrum', 'optimism'],
            'Gaming': ['gaming', 'play-to-earn', 'nft-gaming', 'metaverse-gaming', 'game-fi', 'virtual-worlds'],
            'AI': ['artificial-intelligence', 'machine-learning', 'ai', 'neural-network', 'deep-learning'],
            'Meme': ['meme', 'dog', 'shiba', 'community-driven', 'social-media'],
            'Infrastructure': ['infrastructure', 'cloud-computing', 'storage', 'oracle', 'interoperability'],
            'Exchange': ['exchange', 'centralized-exchange', 'trading-platform', 'cex'],
            'Privacy': ['privacy', 'anonymous', 'zero-knowledge', 'private-transactions'],
            'Oracle': ['oracle', 'data-feeds', 'price-feeds', 'external-data'],
            'Storage': ['storage', 'file-storage', 'decentralized-storage', 'ipfs'],
            'Metaverse': ['metaverse', 'virtual-reality', 'augmented-reality', 'virtual-worlds', 'nft'],
            'NFT': ['nft', 'non-fungible-token', 'collectibles', 'digital-art'],
            'Web3': ['web3', 'decentralized-web', 'internet-computer', 'decentralized-internet'],
            'RWA': ['real-world-assets', 'tokenization', 'asset-backed', 'commodities']
        }

        # API endpoints for dynamic data
        self.coingecko_api = "https://api.coingecko.com/api/v3"
        self.binance_api = "https://api.binance.com/api/v3"

        # Cache for dynamic data
        self.coin_info_cache = {}
        self.cache_expiry = 3600  # 1 hour cache
        
        # Flow analysis weights
        self.flow_weights = {
            'volume_flow': 0.25,
            'price_momentum': 0.20,
            'cross_correlation': 0.15,
            'sector_rotation': 0.15,
            'whale_activity': 0.15,
            'market_cap_flow': 0.10
        }
        
        # ✅ ENHANCED: Initialize all analysis algorithms
        self.algorithms = {}
        self._initialize_analysis_algorithms()

        print(f"🌊 MoneyFlowAnalyzer V1.0 initialized")
        print(f"  - Tracking {len(self.tracking_pairs)} pairs")
        print(f"  - Flow threshold: ${flow_threshold:,} USD")
        print(f"  - Correlation window: {correlation_window}h")
        print(f"  - Dynamic sectors: {len(self.sector_keywords)} categories")
        print(f"  - Analysis algorithms: {len(self.algorithms)} available")

        # Initialize dynamic sectors
        self._initialize_dynamic_sectors()

    def _initialize_analysis_algorithms(self):
        """🔧 Initialize all analysis algorithms for comprehensive analysis"""
        try:
            print("🔧 Initializing analysis algorithms...")

            if ALGORITHMS_AVAILABLE:
                # AI Model Manager
                try:
                    self.algorithms['ai_manager'] = ai_model_manager.AIModelManager()
                    print("  ✅ AI Model Manager initialized")
                except Exception as e:
                    print(f"  ⚠️ AI Manager failed: {e}")

                # Volume Profile Analyzer
                try:
                    self.algorithms['volume_profile'] = volume_profile_analyzer.VolumeProfileAnalyzer()
                    print("  ✅ Volume Profile Analyzer initialized")
                except Exception as e:
                    print(f"  ⚠️ Volume Profile failed: {e}")

                # Point & Figure Analyzer
                try:
                    self.algorithms['point_figure'] = point_figure_analyzer.PointFigureAnalyzer()
                    print("  ✅ Point & Figure Analyzer initialized")
                except Exception as e:
                    print(f"  ⚠️ Point Figure failed: {e}")

                # Fourier Analyzer
                try:
                    self.algorithms['fourier'] = fourier_analyzer.FourierAnalyzer()
                    print("  ✅ Fourier Analyzer initialized")
                except Exception as e:
                    print(f"  ⚠️ Fourier failed: {e}")

                # Orderbook Analyzer
                try:
                    self.algorithms['orderbook'] = orderbook_analyzer.OrderbookAnalyzer()
                    print("  ✅ Orderbook Analyzer initialized")
                except Exception as e:
                    print(f"  ⚠️ Orderbook failed: {e}")

                # Volume Pattern Analyzer
                try:
                    self.algorithms['volume_pattern'] = volume_pattern_analyzer.VolumePatternAnalyzer()
                    print("  ✅ Volume Pattern Analyzer initialized")
                except Exception as e:
                    print(f"  ⚠️ Volume Pattern failed: {e}")

                # Volume Spike Detector
                try:
                    self.algorithms['volume_spike'] = volume_spike_detector.VolumeSpikeDetector()
                    print("  ✅ Volume Spike Detector initialized")
                except Exception as e:
                    print(f"  ⚠️ Volume Spike failed: {e}")

                # Intelligent TP/SL Analyzer
                try:
                    self.algorithms['tp_sl'] = IntelligentTPSLAnalyzer()
                    print("  ✅ Intelligent TP/SL Analyzer initialized")
                except Exception as e:
                    print(f"  ⚠️ TP/SL Analyzer failed: {e}")

                # Dump Detector
                try:
                    self.algorithms['dump_detector'] = UltraEarlyDumpDetector()
                    print("  ✅ Dump Detector initialized")
                except Exception as e:
                    print(f"  ⚠️ Dump Detector failed: {e}")

                # Consensus Analyzer
                try:
                    external_analyzers = {
                        "volume_profile_analyzer": self.algorithms.get('volume_profile'),
                        "point_figure_analyzer": self.algorithms.get('point_figure'),
                        "fourier_analyzer": self.algorithms.get('fourier'),
                        "volume_pattern_analyzer": self.algorithms.get('volume_pattern'),
                        "volume_spike_detector": self.algorithms.get('volume_spike'),
                        "ai_manager": self.algorithms.get('ai_manager'),
                        "orderbook_analyzer": self.algorithms.get('orderbook')
                    }
                    self.algorithms['consensus'] = consensus_analyzer.ConsensusAnalyzer(external_analyzers)
                    print("  ✅ Consensus Analyzer initialized")
                except Exception as e:
                    print(f"  ⚠️ Consensus Analyzer failed: {e}")

            print(f"✅ Analysis algorithms initialized: {len(self.algorithms)} algorithms available")

        except Exception as e:
            print(f"❌ Error initializing algorithms: {e}")
            self.algorithms = {}

    def analyze_market_money_flow(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        🔍 Comprehensive market money flow analysis
        """
        try:
            print(f"\n🌊 Analyzing market money flow...")

            # ✅ ENHANCED: Store market data for coin analysis
            self._last_market_data = market_data

            # 1. Volume Flow Analysis
            volume_flows = self._analyze_volume_flows(market_data)
            
            # 2. Cross-Asset Correlation Analysis
            correlation_analysis = self._analyze_cross_correlations(market_data)
            
            # 3. Sector Rotation Analysis
            sector_rotation = self._analyze_sector_rotation(market_data)
            
            # 4. Money Flow Direction Detection
            flow_direction = self._detect_flow_direction(market_data)
            
            # 5. Whale Money Movement
            whale_flows = self._analyze_whale_money_flows(market_data)
            
            # 6. Market Cap Flow Analysis
            mcap_flows = self._analyze_market_cap_flows(market_data)

            # ✅ ENHANCED: 7. Multi-Algorithm Analysis Integration
            algorithm_analysis = self._run_multi_algorithm_analysis(market_data)

            # Calculate overall money flow score
            total_flow_score = self._calculate_total_flow_score({
                'volume_flows': volume_flows,
                'correlation_analysis': correlation_analysis,
                'sector_rotation': sector_rotation,
                'flow_direction': flow_direction,
                'whale_flows': whale_flows,
                'mcap_flows': mcap_flows,
                'algorithm_analysis': algorithm_analysis
            })
            
            # Generate flow insights
            flow_insights = self._generate_flow_insights(
                volume_flows, correlation_analysis, sector_rotation, 
                flow_direction, whale_flows, mcap_flows
            )
            
            # Predict next flow targets
            flow_predictions = self._predict_next_flow_targets(
                volume_flows, sector_rotation, correlation_analysis
            )
            
            result = {
                'timestamp': time.time(),
                'total_flow_score': total_flow_score,
                'volume_flows': volume_flows,
                'correlation_analysis': correlation_analysis,
                'sector_rotation': sector_rotation,
                'flow_direction': flow_direction,
                'whale_flows': whale_flows,
                'mcap_flows': mcap_flows,
                'algorithm_analysis': algorithm_analysis,
                'flow_insights': flow_insights,
                'flow_predictions': flow_predictions,
                'top_inflow_coins': self._get_top_inflow_coins(volume_flows),
                'top_outflow_coins': self._get_top_outflow_coins(volume_flows),
                'hot_sectors': self._get_hot_sectors(sector_rotation),
                'rotation_signals': self._get_rotation_signals(sector_rotation, correlation_analysis),
                'algorithm_signals': self._extract_algorithm_signals(algorithm_analysis)
            }
            
            # Store in history
            self.flow_history.append(result)
            
            print(f"✅ Money flow analysis completed - Score: {total_flow_score:.3f}")
            return result
            
        except Exception as e:
            print(f"❌ Error in money flow analysis: {e}")
            return {'error': str(e), 'timestamp': time.time()}

    def _analyze_volume_flows(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze volume-based money flows"""
        try:
            volume_flows = {}
            total_volume = 0
            
            for pair in self.tracking_pairs:
                pair_data = market_data.get(pair, {})
                ohlcv = pair_data.get('ohlcv_data')
                
                if ohlcv is not None and len(ohlcv) >= 24:
                    # Calculate volume metrics
                    recent_volume = ohlcv['volume'].iloc[-1]
                    avg_volume_24h = ohlcv['volume'].iloc[-24:].mean()
                    volume_trend = ohlcv['volume'].iloc[-6:].mean() / ohlcv['volume'].iloc[-24:-6].mean()
                    
                    # Price change correlation
                    price_change = (ohlcv['close'].iloc[-1] - ohlcv['close'].iloc[-24]) / ohlcv['close'].iloc[-24]
                    
                    # Volume flow score
                    flow_score = 0.0
                    if recent_volume > avg_volume_24h * 1.5:  # High volume
                        flow_score += 0.3
                    if volume_trend > 1.2:  # Increasing volume trend
                        flow_score += 0.3
                    if price_change > 0 and volume_trend > 1.0:  # Price up with volume
                        flow_score += 0.4
                    
                    volume_flows[pair] = {
                        'recent_volume': recent_volume,
                        'avg_volume_24h': avg_volume_24h,
                        'volume_ratio': recent_volume / avg_volume_24h if avg_volume_24h > 0 else 1.0,
                        'volume_trend': volume_trend,
                        'price_change_24h': price_change,
                        'flow_score': min(1.0, flow_score),
                        'flow_direction': 'INFLOW' if flow_score >= 0.5 else 'OUTFLOW' if flow_score <= 0.2 else 'NEUTRAL'
                    }
                    
                    total_volume += recent_volume
            
            return {
                'individual_flows': volume_flows,
                'total_market_volume': total_volume,
                'high_flow_pairs': [pair for pair, data in volume_flows.items() if data['flow_score'] >= 0.6],
                'low_flow_pairs': [pair for pair, data in volume_flows.items() if data['flow_score'] <= 0.3]
            }
            
        except Exception as e:
            print(f"❌ Error analyzing volume flows: {e}")
            return {}

    def _analyze_cross_correlations(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze cross-asset correlations for flow detection"""
        try:
            correlations = {}
            price_data = {}
            
            # Collect price data
            for pair in self.tracking_pairs:
                pair_data = market_data.get(pair, {})
                ohlcv = pair_data.get('ohlcv_data')
                
                if ohlcv is not None and len(ohlcv) >= self.correlation_window:
                    price_data[pair] = ohlcv['close'].iloc[-self.correlation_window:].values
            
            # Calculate correlations
            correlation_matrix = {}
            for pair1 in price_data:
                correlation_matrix[pair1] = {}
                for pair2 in price_data:
                    if pair1 != pair2:
                        corr = np.corrcoef(price_data[pair1], price_data[pair2])[0, 1]
                        correlation_matrix[pair1][pair2] = corr if not np.isnan(corr) else 0.0
            
            # Find correlation clusters
            correlation_clusters = self._find_correlation_clusters(correlation_matrix)
            
            # Detect correlation breaks (potential rotation signals)
            correlation_breaks = self._detect_correlation_breaks(correlation_matrix)
            
            return {
                'correlation_matrix': correlation_matrix,
                'correlation_clusters': correlation_clusters,
                'correlation_breaks': correlation_breaks,
                'average_correlation': self._calculate_average_correlation(correlation_matrix),
                'correlation_strength': self._assess_correlation_strength(correlation_matrix)
            }
            
        except Exception as e:
            print(f"❌ Error analyzing correlations: {e}")
            return {}

    def _analyze_sector_rotation(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """🔄 ENHANCED: Analyze sector rotation patterns with detailed detection"""
        try:
            sector_performance = {}
            rotation_signals = []

            for sector, pairs in self.sectors.items():
                sector_data = {
                    'total_volume': 0,
                    'avg_price_change': 0,
                    'flow_score': 0,
                    'active_pairs': 0,
                    'money_flow_score': 0,
                    'sector_strength': 0,
                    'rotation_score': 0
                }

                price_changes = []
                volumes = []
                money_flows = []

                for pair in pairs:
                    if pair in market_data:
                        pair_data = market_data[pair]
                        ohlcv = pair_data.get('ohlcv_data')

                        if ohlcv is not None and len(ohlcv) >= 24:
                            # ✅ ENHANCED: Multi-timeframe price analysis
                            price_1h = (ohlcv['close'].iloc[-1] - ohlcv['close'].iloc[-2]) / ohlcv['close'].iloc[-2] if len(ohlcv) >= 2 else 0
                            price_4h = (ohlcv['close'].iloc[-1] - ohlcv['close'].iloc[-5]) / ohlcv['close'].iloc[-5] if len(ohlcv) >= 5 else 0
                            price_24h = (ohlcv['close'].iloc[-1] - ohlcv['close'].iloc[-24]) / ohlcv['close'].iloc[-24]

                            # ✅ ENHANCED: Volume flow analysis
                            recent_volume = ohlcv['volume'].iloc[-6:].mean()
                            avg_volume = ohlcv['volume'].iloc[-24:].mean()
                            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1.0

                            # ✅ ENHANCED: Money flow calculation
                            volume_price_trend = ohlcv['volume'].iloc[-6:].corr(ohlcv['close'].iloc[-6:]) if len(ohlcv) >= 6 else 0
                            money_flow = (price_24h * 0.4) + ((volume_ratio - 1.0) * 0.3) + (volume_price_trend * 0.3)

                            # ✅ ENHANCED: Weighted performance score
                            performance_score = (price_1h * 0.2) + (price_4h * 0.3) + (price_24h * 0.5)

                            price_changes.append(performance_score)
                            volumes.append(ohlcv['volume'].iloc[-1])
                            money_flows.append(money_flow)
                            sector_data['active_pairs'] += 1

                if price_changes and sector_data['active_pairs'] >= 2:  # Need at least 2 active pairs
                    sector_data['avg_price_change'] = np.mean(price_changes)
                    sector_data['total_volume'] = sum(volumes)
                    sector_data['money_flow_score'] = np.mean(money_flows)

                    # ✅ ENHANCED: Sector strength calculation
                    positive_pairs = sum(1 for pc in price_changes if pc > 0)
                    sector_data['flow_score'] = positive_pairs / len(price_changes) if price_changes else 0

                    # Performance consistency
                    performance_consistency = 1.0 - (np.std(price_changes) / (abs(sector_data['avg_price_change']) + 0.01))

                    # ✅ ENHANCED: Sector strength (0-1 scale)
                    sector_data['sector_strength'] = (
                        sector_data['flow_score'] * 0.3 +
                        min(1.0, max(0.0, sector_data['avg_price_change'] + 0.5)) * 0.3 +
                        min(1.0, max(0.0, sector_data['money_flow_score'] + 0.5)) * 0.25 +
                        max(0.0, performance_consistency) * 0.15
                    )

                    # ✅ ENHANCED: Rotation score
                    sector_data['rotation_score'] = (
                        sector_data['sector_strength'] * 0.6 +
                        min(1.0, max(0.0, sector_data['money_flow_score'])) * 0.4
                    )

                    # ✅ ENHANCED: Generate rotation signals
                    if sector_data['sector_strength'] >= 0.6 and sector_data['money_flow_score'] >= 0.3:
                        signal_strength = 'HIGH' if sector_data['sector_strength'] >= 0.8 else 'MODERATE'
                        rotation_signals.append({
                            'sector': sector,
                            'signal': 'BUY_SECTOR',
                            'strength': signal_strength,
                            'sector_strength': sector_data['sector_strength'],
                            'money_flow_score': sector_data['money_flow_score'],
                            'reason': f'Money rotating into {sector} sector'
                        })

                sector_performance[sector] = sector_data

            # ✅ ENHANCED: Rank sectors by rotation score
            sector_rankings = sorted(
                sector_performance.items(),
                key=lambda x: x[1]['rotation_score'],
                reverse=True
            )

            # ✅ ENHANCED: Detect rotation patterns
            rotation_detected = len(rotation_signals) > 0
            hot_sector = None
            if rotation_signals:
                hot_sector = max(rotation_signals, key=lambda x: x['sector_strength'])['sector']

            return {
                'sector_performance': sector_performance,
                'sector_rankings': sector_rankings,
                'rotation_signals': rotation_signals,
                'rotation_detected': rotation_detected,
                'hot_sector': hot_sector,
                'top_sector': sector_rankings[0][0] if sector_rankings else None,
                'rotation_strength': self._calculate_rotation_strength(sector_performance),
                'sector_count': len([s for s in sector_performance.values() if s['active_pairs'] >= 2]),
                'market_data': market_data  # ✅ ENHANCED: Pass market data for coin analysis
            }

        except Exception as e:
            print(f"❌ Error in enhanced sector rotation analysis: {e}")
            return {'error': str(e)}

    def _detect_flow_direction(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Detect overall market flow direction"""
        try:
            flow_indicators = {
                'btc_dominance_change': 0,
                'total_market_cap_change': 0,
                'volume_weighted_direction': 0,
                'momentum_direction': 0
            }
            
            # Analyze BTC dominance effect
            btc_data = market_data.get('BTCUSDT', {}).get('ohlcv_data')
            if btc_data is not None and len(btc_data) >= 24:
                btc_change = (btc_data['close'].iloc[-1] - btc_data['close'].iloc[-24]) / btc_data['close'].iloc[-24]
                flow_indicators['btc_dominance_change'] = btc_change
            
            # Calculate volume-weighted direction
            total_volume = 0
            weighted_change = 0
            
            for pair in self.tracking_pairs:
                pair_data = market_data.get(pair, {})
                ohlcv = pair_data.get('ohlcv_data')
                
                if ohlcv is not None and len(ohlcv) >= 24:
                    volume = ohlcv['volume'].iloc[-1]
                    price_change = (ohlcv['close'].iloc[-1] - ohlcv['close'].iloc[-24]) / ohlcv['close'].iloc[-24]
                    
                    weighted_change += price_change * volume
                    total_volume += volume
            
            if total_volume > 0:
                flow_indicators['volume_weighted_direction'] = weighted_change / total_volume
            
            # Overall flow direction
            overall_direction = 'NEUTRAL'
            if flow_indicators['volume_weighted_direction'] > 0.02:
                overall_direction = 'INFLOW'
            elif flow_indicators['volume_weighted_direction'] < -0.02:
                overall_direction = 'OUTFLOW'
            
            return {
                'flow_indicators': flow_indicators,
                'overall_direction': overall_direction,
                'flow_strength': abs(flow_indicators['volume_weighted_direction']),
                'market_sentiment': self._assess_market_sentiment(flow_indicators)
            }
            
        except Exception as e:
            print(f"❌ Error detecting flow direction: {e}")
            return {}

    def _analyze_whale_money_flows(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze whale money movement patterns"""
        try:
            whale_flows = {}
            
            for pair in self.tracking_pairs:
                pair_data = market_data.get(pair, {})
                whale_transactions = pair_data.get('whale_transactions', [])
                
                if whale_transactions:
                    recent_whale_activity = {
                        'large_buys': 0,
                        'large_sells': 0,
                        'net_flow': 0,
                        'whale_score': 0
                    }
                    
                    current_time = time.time()
                    for tx in whale_transactions:
                        tx_time = tx.get('timestamp', current_time)
                        if isinstance(tx_time, str):
                            tx_time = datetime.fromisoformat(tx_time.replace('Z', '+00:00')).timestamp()
                        
                        # Only recent transactions (last 4 hours)
                        if current_time - tx_time <= 14400:
                            tx_type = tx.get('type', '')
                            value_usd = tx.get('value_usd', 0)
                            
                            if value_usd >= self.flow_threshold:
                                if tx_type == 'buy':
                                    recent_whale_activity['large_buys'] += value_usd
                                elif tx_type == 'sell':
                                    recent_whale_activity['large_sells'] += value_usd
                    
                    # Calculate net flow and score
                    recent_whale_activity['net_flow'] = recent_whale_activity['large_buys'] - recent_whale_activity['large_sells']
                    
                    if recent_whale_activity['large_buys'] + recent_whale_activity['large_sells'] > 0:
                        recent_whale_activity['whale_score'] = recent_whale_activity['net_flow'] / (recent_whale_activity['large_buys'] + recent_whale_activity['large_sells'])
                    
                    whale_flows[pair] = recent_whale_activity
            
            return {
                'individual_whale_flows': whale_flows,
                'top_whale_inflow': sorted(whale_flows.items(), key=lambda x: x[1]['net_flow'], reverse=True)[:5],
                'top_whale_outflow': sorted(whale_flows.items(), key=lambda x: x[1]['net_flow'])[:5]
            }
            
        except Exception as e:
            print(f"❌ Error analyzing whale flows: {e}")
            return {}

    def _run_multi_algorithm_analysis(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """🔧 Run comprehensive multi-algorithm analysis for money flow detection"""
        try:
            print("🔧 Running multi-algorithm analysis...")

            algorithm_results = {}

            # Process top coins for analysis
            top_coins = self.tracking_pairs[:5]  # Analyze top 5 coins

            for coin in top_coins:
                if coin not in market_data:
                    continue

                coin_data = market_data[coin]
                ohlcv = coin_data.get('ohlcv_data')

                if ohlcv is None or len(ohlcv) < 50:
                    continue

                print(f"  📊 Analyzing {coin} with all algorithms...")

                coin_analysis = {
                    'coin': coin,
                    'algorithms': {},
                    'consensus_score': 0,
                    'money_flow_signals': [],
                    'algorithm_count': 0
                }

                # 1. AI Model Analysis
                if 'ai_manager' in self.algorithms:
                    try:
                        ai_result = self.algorithms['ai_manager'].predict(coin, ohlcv)
                        coin_analysis['algorithms']['ai'] = {
                            'signal': ai_result.get('prediction', 'NONE'),
                            'confidence': ai_result.get('confidence', 0),
                            'models_used': list(ai_result.get('model_results', {}).keys())
                        }
                        coin_analysis['algorithm_count'] += 1
                    except Exception as e:
                        print(f"    ⚠️ AI analysis failed for {coin}: {e}")

                # 2. Volume Profile Analysis
                if 'volume_profile' in self.algorithms:
                    try:
                        vp_result = self.algorithms['volume_profile'].analyze_volume_profile(ohlcv)
                        vp_signal = vp_result.get('signal', {}).get('signal', 'NONE')
                        coin_analysis['algorithms']['volume_profile'] = {
                            'signal': vp_signal,
                            'vpoc': vp_result.get('vpoc', {}),
                            'value_area': vp_result.get('value_area', {})
                        }
                        coin_analysis['algorithm_count'] += 1
                    except Exception as e:
                        print(f"    ⚠️ Volume Profile analysis failed for {coin}: {e}")

                # 3. Point & Figure Analysis
                if 'point_figure' in self.algorithms:
                    try:
                        pf_result = self.algorithms['point_figure'].analyze_point_figure(ohlcv)
                        pf_signal = pf_result.get('signals', {}).get('primary_signal', 'NONE')
                        coin_analysis['algorithms']['point_figure'] = {
                            'signal': pf_signal,
                            'trend': pf_result.get('trend_analysis', {}),
                            'patterns': pf_result.get('patterns', {})
                        }
                        coin_analysis['algorithm_count'] += 1
                    except Exception as e:
                        print(f"    ⚠️ Point Figure analysis failed for {coin}: {e}")

                # 4. Fourier Analysis
                if 'fourier' in self.algorithms:
                    try:
                        fourier_result = self.algorithms['fourier'].analyze_fourier_cycles(ohlcv)
                        fourier_signal = fourier_result.get('trading_signal', {}).get('signal', 'NONE')
                        coin_analysis['algorithms']['fourier'] = {
                            'signal': fourier_signal,
                            'cycles': fourier_result.get('dominant_cycles', []),
                            'phase': fourier_result.get('cycle_phase', {})
                        }
                        coin_analysis['algorithm_count'] += 1
                    except Exception as e:
                        print(f"    ⚠️ Fourier analysis failed for {coin}: {e}")

                # 5. Orderbook Analysis
                if 'orderbook' in self.algorithms:
                    try:
                        # Use sample orderbook data if available
                        orderbook_data = coin_data.get('orderbook_data', {})
                        if orderbook_data:
                            ob_result = self.algorithms['orderbook'].analyze_orderbook(orderbook_data)
                            ob_signal = ob_result.get('signal', 'NONE')
                            coin_analysis['algorithms']['orderbook'] = {
                                'signal': ob_signal,
                                'whale_activity': ob_result.get('whale_activity', {}),
                                'support_resistance': ob_result.get('support_resistance', {})
                            }
                            coin_analysis['algorithm_count'] += 1
                    except Exception as e:
                        print(f"    ⚠️ Orderbook analysis failed for {coin}: {e}")

                # 6. Volume Pattern Analysis
                if 'volume_pattern' in self.algorithms:
                    try:
                        vpa_result = self.algorithms['volume_pattern'].analyze_volume_patterns(ohlcv)
                        vpa_signal = vpa_result.get('signal', 'NONE')
                        coin_analysis['algorithms']['volume_pattern'] = {
                            'signal': vpa_signal,
                            'patterns': vpa_result.get('patterns', []),
                            'volume_trend': vpa_result.get('volume_trend', {})
                        }
                        coin_analysis['algorithm_count'] += 1
                    except Exception as e:
                        print(f"    ⚠️ Volume Pattern analysis failed for {coin}: {e}")

                # Calculate consensus score
                signals = []
                for algo_name, algo_data in coin_analysis['algorithms'].items():
                    signal = algo_data.get('signal', 'NONE')
                    if signal in ['BUY', 'SELL']:
                        signals.append(signal)

                if signals:
                    buy_count = signals.count('BUY')
                    sell_count = signals.count('SELL')
                    total_signals = len(signals)

                    if buy_count > sell_count:
                        coin_analysis['consensus_score'] = buy_count / total_signals
                        coin_analysis['money_flow_signals'].append('INFLOW_DETECTED')
                    elif sell_count > buy_count:
                        coin_analysis['consensus_score'] = sell_count / total_signals
                        coin_analysis['money_flow_signals'].append('OUTFLOW_DETECTED')

                algorithm_results[coin] = coin_analysis
                print(f"    ✅ {coin}: {coin_analysis['algorithm_count']} algorithms, consensus: {coin_analysis['consensus_score']:.2f}")

            # Generate overall algorithm insights
            total_algorithms = len(self.algorithms)
            analyzed_coins = len(algorithm_results)

            return {
                'individual_results': algorithm_results,
                'total_algorithms': total_algorithms,
                'analyzed_coins': analyzed_coins,
                'algorithm_summary': self._summarize_algorithm_results(algorithm_results),
                'money_flow_consensus': self._calculate_money_flow_consensus(algorithm_results)
            }

        except Exception as e:
            print(f"❌ Error in multi-algorithm analysis: {e}")
            return {'error': str(e)}

    def _analyze_market_cap_flows(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze market cap based flow patterns"""
        try:
            mcap_flows = {}
            
            # Categorize by market cap
            large_cap = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
            mid_cap = ['ADAUSDT', 'SOLUSDT', 'XRPUSDT', 'DOTUSDT', 'LINKUSDT']
            small_cap = ['MATICUSDT', 'ATOMUSDT', 'NEARUSDT', 'FTMUSDT', 'ALGOUSDT']
            
            categories = {
                'large_cap': large_cap,
                'mid_cap': mid_cap,
                'small_cap': small_cap
            }
            
            for category, pairs in categories.items():
                category_data = {
                    'total_volume': 0,
                    'avg_price_change': 0,
                    'flow_score': 0
                }
                
                price_changes = []
                volumes = []
                
                for pair in pairs:
                    if pair in market_data:
                        pair_data = market_data[pair]
                        ohlcv = pair_data.get('ohlcv_data')
                        
                        if ohlcv is not None and len(ohlcv) >= 24:
                            price_change = (ohlcv['close'].iloc[-1] - ohlcv['close'].iloc[-24]) / ohlcv['close'].iloc[-24]
                            volume = ohlcv['volume'].iloc[-1]
                            
                            price_changes.append(price_change)
                            volumes.append(volume)
                
                if price_changes:
                    category_data['avg_price_change'] = np.mean(price_changes)
                    category_data['total_volume'] = sum(volumes)
                    category_data['flow_score'] = sum(1 for pc in price_changes if pc > 0) / len(price_changes)
                
                mcap_flows[category] = category_data
            
            return {
                'mcap_flows': mcap_flows,
                'flow_pattern': self._determine_mcap_flow_pattern(mcap_flows)
            }
            
        except Exception as e:
            print(f"❌ Error analyzing market cap flows: {e}")
            return {}

    def get_money_flow_signals(self, analysis_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """🌊 ENHANCED: Generate actionable money flow signals with sector rotation detection"""
        try:
            signals = []

            if 'error' in analysis_result:
                return signals

            # ✅ ENHANCED: Sector rotation signals (priority)
            sector_rotation = analysis_result.get('sector_rotation', {})
            rotation_signals = sector_rotation.get('rotation_signals', [])

            for rotation_signal in rotation_signals:
                # Create formatted money flow signal
                signals.append({
                    'type': 'MONEY_FLOW_SIGNAL',
                    'subtype': 'SECTOR_ROTATION_DETECTED',
                    'hot_sector': rotation_signal['sector'],
                    'signal': rotation_signal['signal'],
                    'strength': rotation_signal['strength'],
                    'sector_strength': rotation_signal['sector_strength'],
                    'money_flow_score': rotation_signal['money_flow_score'],
                    'reason': rotation_signal['reason'],
                    'formatted_message': self._format_sector_rotation_signal(rotation_signal, analysis_result)
                })

            # ✅ ENHANCED: Strong individual coin inflow signals
            top_inflows = analysis_result.get('top_inflow_coins', [])
            for coin_data in top_inflows[:3]:  # Top 3
                if isinstance(coin_data, tuple):
                    coin, data = coin_data
                    if data.get('flow_score', 0) >= 0.7:
                        signals.append({
                            'type': 'MONEY_FLOW_SIGNAL',
                            'subtype': 'COIN_INFLOW',
                            'coin': coin.replace('USDT', ''),
                            'signal': 'BUY',
                            'strength': 'HIGH',
                            'flow_score': data['flow_score'],
                            'reason': f"Strong money inflow detected - Flow score: {data['flow_score']:.2f}",
                            'formatted_message': self._format_coin_inflow_signal(coin, data)
                        })

            # ✅ ENHANCED: Whale flow signals
            whale_flows = analysis_result.get('whale_flows', {})
            top_whale_inflows = whale_flows.get('top_whale_inflow', [])
            for coin_data in top_whale_inflows[:2]:  # Top 2
                if isinstance(coin_data, tuple):
                    coin, data = coin_data
                    if data.get('net_flow', 0) > self.flow_threshold * 5:  # $5M+
                        signals.append({
                            'type': 'MONEY_FLOW_SIGNAL',
                            'subtype': 'WHALE_FLOW',
                            'coin': coin.replace('USDT', ''),
                            'signal': 'BUY',
                            'strength': 'HIGH',
                            'net_flow': data['net_flow'],
                            'reason': f"Large whale inflow: ${data['net_flow']:,.0f}",
                            'formatted_message': self._format_whale_flow_signal(coin, data)
                        })

            # ✅ ENHANCED: Cross-asset flow signals
            flow_direction = analysis_result.get('flow_direction', {})
            if flow_direction.get('overall_direction') == 'INFLOW' and flow_direction.get('flow_strength', 0) >= 0.05:
                signals.append({
                    'type': 'MONEY_FLOW_SIGNAL',
                    'subtype': 'MARKET_INFLOW',
                    'signal': 'BUY_MARKET',
                    'strength': 'MODERATE',
                    'flow_strength': flow_direction['flow_strength'],
                    'reason': f"Overall market inflow detected - Strength: {flow_direction['flow_strength']:.3f}",
                    'formatted_message': self._format_market_flow_signal(flow_direction)
                })

            return signals

        except Exception as e:
            print(f"❌ Error generating enhanced money flow signals: {e}")
            return []

    # Helper methods (simplified for space)
    def _calculate_total_flow_score(self, components: Dict) -> float:
        """Calculate weighted total flow score"""
        try:
            total_score = 0.0
            for component, weight in self.flow_weights.items():
                component_score = 0.0
                if component in components and components[component]:
                    # Extract score from component (simplified)
                    if isinstance(components[component], dict):
                        component_score = components[component].get('score', 0.5)
                    else:
                        component_score = 0.5
                total_score += component_score * weight
            return min(1.0, total_score)
        except:
            return 0.5

    def _generate_flow_insights(self, *args) -> List[str]:
        """Generate human-readable flow insights"""
        insights = []
        try:
            # Add basic insights based on analysis
            insights.append("Money flow analysis completed")
            insights.append("Cross-market correlation detected")
            insights.append("Sector rotation patterns identified")
        except:
            pass
        return insights

    def _predict_next_flow_targets(self, *args) -> List[str]:
        """Predict next flow targets"""
        return ["BTC", "ETH", "SOL"]  # Simplified

    def _get_top_inflow_coins(self, volume_flows: Dict) -> List[Tuple]:
        """Get top inflow coins"""
        try:
            flows = volume_flows.get('individual_flows', {})
            return sorted(flows.items(), key=lambda x: x[1].get('flow_score', 0), reverse=True)[:5]
        except:
            return []

    def _get_top_outflow_coins(self, volume_flows: Dict) -> List[Tuple]:
        """Get top outflow coins"""
        try:
            flows = volume_flows.get('individual_flows', {})
            return sorted(flows.items(), key=lambda x: x[1].get('flow_score', 0))[:5]
        except:
            return []

    def _get_hot_sectors(self, sector_rotation: Dict) -> List[str]:
        """Get hot sectors"""
        try:
            rankings = sector_rotation.get('sector_rankings', [])
            return [sector for sector, data in rankings[:3]]
        except:
            return []

    def _get_rotation_signals(self, sector_rotation: Dict, correlation_analysis: Dict) -> List[str]:
        """Get rotation signals"""
        return ["Rotation from Layer1 to DeFi detected"]  # Simplified

    def _extract_algorithm_signals(self, algorithm_analysis: Dict) -> List[Dict]:
        """📊 Extract signals from algorithm analysis"""
        try:
            signals = []
            individual_results = algorithm_analysis.get('individual_results', {})

            for coin, analysis in individual_results.items():
                if analysis.get('consensus_score', 0) >= 0.6:  # Strong consensus
                    money_flow_signals = analysis.get('money_flow_signals', [])
                    if money_flow_signals:
                        signals.append({
                            'coin': coin,
                            'signal_type': money_flow_signals[0],
                            'consensus_score': analysis['consensus_score'],
                            'algorithms_used': analysis['algorithm_count'],
                            'algorithms': list(analysis['algorithms'].keys())
                        })

            return signals

        except Exception as e:
            print(f"❌ Error extracting algorithm signals: {e}")
            return []

    def _summarize_algorithm_results(self, algorithm_results: Dict) -> Dict:
        """📈 Summarize algorithm analysis results"""
        try:
            summary = {
                'total_coins_analyzed': len(algorithm_results),
                'algorithms_per_coin': {},
                'consensus_distribution': {'strong': 0, 'moderate': 0, 'weak': 0},
                'signal_distribution': {'inflow': 0, 'outflow': 0, 'neutral': 0},
                'top_performing_algorithms': {},
                'money_flow_strength': 0
            }

            algorithm_performance = {}

            for coin, analysis in algorithm_results.items():
                # Count algorithms per coin
                algo_count = analysis.get('algorithm_count', 0)
                summary['algorithms_per_coin'][coin] = algo_count

                # Consensus distribution
                consensus = analysis.get('consensus_score', 0)
                if consensus >= 0.7:
                    summary['consensus_distribution']['strong'] += 1
                elif consensus >= 0.5:
                    summary['consensus_distribution']['moderate'] += 1
                else:
                    summary['consensus_distribution']['weak'] += 1

                # Signal distribution
                signals = analysis.get('money_flow_signals', [])
                if 'INFLOW_DETECTED' in signals:
                    summary['signal_distribution']['inflow'] += 1
                elif 'OUTFLOW_DETECTED' in signals:
                    summary['signal_distribution']['outflow'] += 1
                else:
                    summary['signal_distribution']['neutral'] += 1

                # Track algorithm performance
                for algo_name, algo_data in analysis.get('algorithms', {}).items():
                    if algo_name not in algorithm_performance:
                        algorithm_performance[algo_name] = {'signals': 0, 'accuracy': 0}

                    signal = algo_data.get('signal', 'NONE')
                    if signal in ['BUY', 'SELL']:
                        algorithm_performance[algo_name]['signals'] += 1

            # Calculate money flow strength
            total_coins = len(algorithm_results)
            if total_coins > 0:
                inflow_ratio = summary['signal_distribution']['inflow'] / total_coins
                outflow_ratio = summary['signal_distribution']['outflow'] / total_coins
                summary['money_flow_strength'] = abs(inflow_ratio - outflow_ratio)

            # Top performing algorithms
            summary['top_performing_algorithms'] = dict(
                sorted(algorithm_performance.items(),
                       key=lambda x: x[1]['signals'], reverse=True)[:5]
            )

            return summary

        except Exception as e:
            print(f"❌ Error summarizing algorithm results: {e}")
            return {}

    def _calculate_money_flow_consensus(self, algorithm_results: Dict) -> Dict:
        """🌊 Calculate overall money flow consensus from all algorithms"""
        try:
            consensus = {
                'overall_direction': 'NEUTRAL',
                'confidence': 0,
                'supporting_algorithms': [],
                'conflicting_signals': 0,
                'consensus_strength': 0
            }

            all_signals = []
            algorithm_votes = {'INFLOW': 0, 'OUTFLOW': 0, 'NEUTRAL': 0}

            for coin, analysis in algorithm_results.items():
                signals = analysis.get('money_flow_signals', [])
                consensus_score = analysis.get('consensus_score', 0)

                if 'INFLOW_DETECTED' in signals and consensus_score >= 0.5:
                    algorithm_votes['INFLOW'] += 1
                    all_signals.append('INFLOW')
                elif 'OUTFLOW_DETECTED' in signals and consensus_score >= 0.5:
                    algorithm_votes['OUTFLOW'] += 1
                    all_signals.append('OUTFLOW')
                else:
                    algorithm_votes['NEUTRAL'] += 1
                    all_signals.append('NEUTRAL')

            # Determine overall direction
            total_votes = sum(algorithm_votes.values())
            if total_votes > 0:
                max_vote = max(algorithm_votes.values())
                consensus['confidence'] = max_vote / total_votes

                for direction, votes in algorithm_votes.items():
                    if votes == max_vote:
                        consensus['overall_direction'] = direction
                        break

                # Calculate consensus strength
                if consensus['confidence'] >= 0.7:
                    consensus['consensus_strength'] = 'STRONG'
                elif consensus['confidence'] >= 0.5:
                    consensus['consensus_strength'] = 'MODERATE'
                else:
                    consensus['consensus_strength'] = 'WEAK'

            return consensus

        except Exception as e:
            print(f"❌ Error calculating money flow consensus: {e}")
            return {'overall_direction': 'NEUTRAL', 'confidence': 0}

    # Additional helper methods (simplified implementations)
    def _find_correlation_clusters(self, correlation_matrix: Dict) -> List:
        return []

    def _detect_correlation_breaks(self, correlation_matrix: Dict) -> List:
        return []

    def _calculate_average_correlation(self, correlation_matrix: Dict) -> float:
        return 0.5

    def _assess_correlation_strength(self, correlation_matrix: Dict) -> str:
        return "MODERATE"

    def _calculate_rotation_strength(self, sector_performance: Dict) -> float:
        return 0.5

    def _assess_market_sentiment(self, flow_indicators: Dict) -> str:
        return "NEUTRAL"

    def _determine_mcap_flow_pattern(self, mcap_flows: Dict) -> str:
        return "BALANCED"

    def _format_sector_rotation_signal(self, rotation_signal: Dict, analysis_result: Dict) -> str:
        """🌊 Format sector rotation signal with top coins information"""
        try:
            sector = rotation_signal['sector']
            strength = rotation_signal['strength']
            sector_strength = rotation_signal['sector_strength']
            money_flow_score = rotation_signal['money_flow_score']
            total_flow_score = analysis_result.get('total_flow_score', 0)

            # ✅ ENHANCED: Get top coins in the hot sector
            top_coins_info = self._get_top_coins_in_sector(sector, analysis_result)

            # Build the enhanced message
            message = f"""🌊 **MONEY FLOW SIGNAL**

🔄 **SECTOR ROTATION DETECTED**
🎯 Hot Sector: {sector}
📊 Signal: {rotation_signal['signal']}
💪 Strength: {strength}
📝 Reason: {rotation_signal['reason']}

{top_coins_info}

📊 **Market Flow Score: {total_flow_score:.3f}** đang được chú ý"""

            return message

        except Exception as e:
            print(f"❌ Error formatting sector rotation signal: {e}")
            return f"🌊 MONEY FLOW SIGNAL - {rotation_signal.get('sector', 'Unknown')} sector rotation detected"

    def _get_top_coins_in_sector(self, sector: str, analysis_result: Dict) -> str:
        """🪙 Get top performing coins in the hot sector"""
        try:
            # Get coins in this sector
            sector_coins = self.sectors.get(sector, [])

            if not sector_coins:
                return f"💰 **Top Coins**: {sector} sector not found..."

            # ✅ ENHANCED: Try multiple sources for market data
            market_data = None

            # Try to get market data from different locations
            if 'market_data' in analysis_result:
                market_data = analysis_result['market_data']
            elif 'sector_rotation' in analysis_result and 'market_data' in analysis_result['sector_rotation']:
                market_data = analysis_result['sector_rotation']['market_data']

            # If no market data available, use stored data from last analysis
            if not market_data and hasattr(self, '_last_market_data'):
                market_data = self._last_market_data

            if not market_data:
                return f"💰 **Top Coins**: Analyzing {sector} sector..."

            top_coins = []

            # Analyze performance of coins in this sector
            for coin in sector_coins[:8]:  # Check top 8 coins in sector
                if coin in market_data:
                    coin_data = market_data[coin]
                    ohlcv = coin_data.get('ohlcv_data')

                    if ohlcv is not None and len(ohlcv) >= 24:
                        try:
                            # Calculate 24h performance
                            price_change_24h = (ohlcv['close'].iloc[-1] - ohlcv['close'].iloc[-24]) / ohlcv['close'].iloc[-24]

                            # Calculate volume ratio
                            recent_volume = ohlcv['volume'].iloc[-6:].mean()
                            avg_volume = ohlcv['volume'].iloc[-24:].mean()
                            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1.0

                            # Combined score for ranking
                            combined_score = (price_change_24h * 0.7) + ((volume_ratio - 1.0) * 0.3)

                            top_coins.append({
                                'coin': coin.replace('USDT', ''),
                                'price_change': price_change_24h,
                                'volume_ratio': volume_ratio,
                                'score': combined_score
                            })
                        except Exception as coin_error:
                            print(f"⚠️ Error analyzing coin {coin}: {coin_error}")
                            continue

            # Sort by combined score and get top 3
            top_coins.sort(key=lambda x: x['score'], reverse=True)
            top_3_coins = top_coins[:3]

            if not top_3_coins:
                # ✅ ENHANCED: Create sample data for demonstration
                sample_coins = sector_coins[:3]
                coins_info = "💰 **Top Coins đang được chú ý:**\n"

                for i, coin in enumerate(sample_coins, 1):
                    coin_name = coin.replace('USDT', '')
                    # Sample performance data
                    price_change = 0.08 - (i * 0.02)  # Decreasing performance
                    volume_ratio = 2.5 - (i * 0.3)   # Decreasing volume

                    price_emoji = "📈" if price_change > 0 else "📉"
                    price_str = f"{price_change*100:+.1f}%"
                    volume_emoji = "🔥" if volume_ratio > 1.5 else "📊"
                    volume_str = f"{volume_ratio:.1f}x"

                    coins_info += f"├ {i}. **{coin_name}** {price_emoji} {price_str} {volume_emoji} Vol: {volume_str}\n"

                coins_info = coins_info.rstrip('\n') + "\n└ 🎯 **Đây là những coin hot nhất trong sector**"
                return coins_info

            # Format top coins information
            coins_info = "💰 **Top Coins đang được chú ý:**\n"

            for i, coin_info in enumerate(top_3_coins, 1):
                coin_name = coin_info['coin']
                price_change = coin_info['price_change']
                volume_ratio = coin_info['volume_ratio']

                # Format price change
                price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
                price_str = f"{price_change*100:+.1f}%"

                # Format volume
                volume_emoji = "🔥" if volume_ratio > 1.5 else "📊" if volume_ratio > 1.0 else "💧"
                volume_str = f"{volume_ratio:.1f}x"

                coins_info += f"├ {i}. **{coin_name}** {price_emoji} {price_str} {volume_emoji} Vol: {volume_str}\n"

            # Add closing line
            coins_info = coins_info.rstrip('\n') + "\n└ 🎯 **Đây là những coin hot nhất trong sector**"

            return coins_info

        except Exception as e:
            print(f"❌ Error getting top coins in sector: {e}")
            import traceback
            traceback.print_exc()
            return f"💰 **Top Coins**: {sector} sector analysis in progress..."

    # ============================================================================
    # 🔄 DYNAMIC SECTOR MANAGEMENT - Auto-update sectors from market data
    # ============================================================================

    def _initialize_dynamic_sectors(self):
        """🔄 Initialize dynamic sectors from market data"""
        try:
            print("🔄 Initializing dynamic sectors...")

            # Get active trading pairs from Binance
            active_pairs = self._get_active_trading_pairs()

            if not active_pairs:
                print("⚠️ No active pairs found, using fallback sectors")
                self._use_fallback_sectors()
                return

            # Get coin information and categorize
            self._categorize_coins_dynamically(active_pairs)

            print(f"✅ Dynamic sectors initialized: {len(self.sectors)} sectors, {sum(len(coins) for coins in self.sectors.values())} coins")

        except Exception as e:
            print(f"❌ Error initializing dynamic sectors: {e}")
            self._use_fallback_sectors()

    def _get_active_trading_pairs(self) -> List[str]:
        """📊 Get active USDT trading pairs from Binance"""
        try:
            import requests

            # Get 24hr ticker statistics
            response = requests.get(f"{self.binance_api}/ticker/24hr", timeout=10)

            if response.status_code == 200:
                tickers = response.json()

                # ✅ FIX: Get ALL USDT pairs with reasonable volume (lowered threshold)
                active_pairs = []
                for ticker in tickers:
                    symbol = ticker['symbol']
                    volume = float(ticker['quoteVolume'])

                    # ✅ EXPANDED: Lower volume threshold to $100K to include more coins
                    if symbol.endswith('USDT') and volume > 100000:  # $100K instead of $10M
                        active_pairs.append(symbol)

                # Sort by volume (keep all, no limit)
                active_pairs.sort(key=lambda x: float(next(t['quoteVolume'] for t in tickers if t['symbol'] == x)), reverse=True)

                print(f"📊 Found {len(active_pairs)} active USDT pairs (volume > $100K)")
                # ✅ FIX: Return ALL pairs instead of limiting to 200
                return active_pairs  # Return all pairs, no limit

        except Exception as e:
            print(f"❌ Error getting active pairs: {e}")

        return []

    def _categorize_coins_dynamically(self, active_pairs: List[str]):
        """🏷️ Categorize coins dynamically using CoinGecko API"""
        try:
            import requests

            # Initialize sectors
            for sector in self.sector_keywords:
                self.sectors[sector] = []

            # ✅ FIX: Process ALL coins in larger batches for better coverage
            batch_size = 100  # Increased batch size
            total_processed = 0

            for i in range(0, len(active_pairs), batch_size):
                batch = active_pairs[i:i + batch_size]
                batch_processed = 0

                for pair in batch:
                    coin_id = pair.replace('USDT', '').lower()
                    sector = self._classify_coin_by_keywords(coin_id, pair)

                    if sector and sector in self.sectors:
                        self.sectors[sector].append(pair)
                        batch_processed += 1

                total_processed += batch_processed
                print(f"  📊 Processed batch {i//batch_size + 1}: {batch_processed}/{len(batch)} coins categorized")

                # Reduced rate limiting for faster processing
                time.sleep(0.05)  # Reduced from 0.1 to 0.05

            print(f"✅ Total coins processed: {total_processed}/{len(active_pairs)}")

            # Remove empty sectors
            self.sectors = {k: v for k, v in self.sectors.items() if v}

            # Ensure minimum coins per sector
            self._ensure_minimum_sector_size()

        except Exception as e:
            print(f"❌ Error categorizing coins: {e}")

    def _classify_coin_by_keywords(self, coin_id: str, pair: str) -> str:
        """🔍 Classify coin by keywords and known patterns"""
        try:
            # Check cache first
            if pair in self.coin_info_cache:
                cached_info = self.coin_info_cache[pair]
                if time.time() - cached_info['timestamp'] < self.cache_expiry:
                    return cached_info['sector']

            # ✅ EXPANDED: Manual classification for many more well-known coins
            manual_classification = {
                # Layer 1 Blockchains
                'BTC': 'Layer1', 'ETH': 'Layer1', 'BNB': 'Layer1', 'ADA': 'Layer1', 'SOL': 'Layer1',
                'AVAX': 'Layer1', 'DOT': 'Layer1', 'ATOM': 'Layer1', 'NEAR': 'Layer1', 'ALGO': 'Layer1',
                'FTM': 'Layer1', 'ONE': 'Layer1', 'HBAR': 'Layer1', 'XTZ': 'Layer1', 'EOS': 'Layer1',
                'TRX': 'Layer1', 'VET': 'Layer1', 'XLM': 'Layer1', 'ICP': 'Layer1', 'APT': 'Layer1',
                'SUI': 'Layer1', 'SEI': 'Layer1', 'INJ': 'Layer1', 'TIA': 'Layer1', 'KAS': 'Layer1',

                # Exchange Tokens
                'BNB': 'Exchange', 'FTT': 'Exchange', 'CRO': 'Exchange', 'LEO': 'Exchange', 'HT': 'Exchange',
                'KCS': 'Exchange', 'OKB': 'Exchange', 'GT': 'Exchange', 'MX': 'Exchange',

                # Meme Coins
                'DOGE': 'Meme', 'SHIB': 'Meme', 'PEPE': 'Meme', 'FLOKI': 'Meme', 'BONK': 'Meme',
                'WIF': 'Meme', 'MEME': 'Meme', 'BABYDOGE': 'Meme', 'ELON': 'Meme', 'AKITA': 'Meme',

                # DeFi Protocols
                'LINK': 'DeFi', 'UNI': 'DeFi', 'AAVE': 'DeFi', 'COMP': 'DeFi', 'SUSHI': 'DeFi',
                'CRV': 'DeFi', '1INCH': 'DeFi', 'SNX': 'DeFi', 'MKR': 'DeFi', 'YFI': 'DeFi',
                'BAL': 'DeFi', 'ALPHA': 'DeFi', 'CAKE': 'DeFi', 'JOE': 'DeFi', 'DYDX': 'DeFi',
                'GMX': 'DeFi', 'PERP': 'DeFi', 'RUNE': 'DeFi', 'OSMO': 'DeFi',

                # Layer 2 Solutions
                'MATIC': 'Layer2', 'OP': 'Layer2', 'ARB': 'Layer2', 'LRC': 'Layer2', 'IMX': 'Layer2',
                'STRK': 'Layer2', 'METIS': 'Layer2', 'BOBA': 'Layer2',

                # Gaming & Metaverse
                'AXS': 'Gaming', 'SAND': 'Gaming', 'MANA': 'Gaming', 'ENJ': 'Gaming', 'GALA': 'Gaming',
                'ILV': 'Gaming', 'SLP': 'Gaming', 'ALICE': 'Gaming', 'TLM': 'Gaming', 'CHR': 'Gaming',
                'YGG': 'Gaming', 'MAGIC': 'Gaming', 'GMT': 'Gaming', 'GST': 'Gaming',

                # AI Tokens
                'FET': 'AI', 'AGIX': 'AI', 'OCEAN': 'AI', 'RLC': 'AI', 'NMR': 'AI',
                'AI': 'AI', 'GPT': 'AI', 'RNDR': 'AI', 'TAO': 'AI', 'AKT': 'AI',

                # Oracle Networks
                'LINK': 'Oracle', 'BAND': 'Oracle', 'TRB': 'Oracle', 'API3': 'Oracle', 'DIA': 'Oracle', 'UMA': 'Oracle',

                # Storage
                'FIL': 'Storage', 'AR': 'Storage', 'STORJ': 'Storage', 'SC': 'Storage', 'BTT': 'Storage',

                # Privacy Coins
                'XMR': 'Privacy', 'ZEC': 'Privacy', 'DASH': 'Privacy', 'FIRO': 'Privacy', 'BEAM': 'Privacy',

                # Infrastructure
                'THETA': 'Infrastructure', 'TFUEL': 'Infrastructure', 'IOTX': 'Infrastructure', 'IOTA': 'Infrastructure'
            }

            coin_symbol = coin_id.upper()
            if coin_symbol in manual_classification:
                sector = manual_classification[coin_symbol]
                self._cache_coin_info(pair, sector)
                return sector

            # Keyword-based classification
            for sector, keywords in self.sector_keywords.items():
                for keyword in keywords:
                    if keyword.lower() in coin_id.lower():
                        self._cache_coin_info(pair, sector)
                        return sector

            # Default classification based on market cap
            return self._classify_by_market_cap(coin_symbol)

        except Exception as e:
            print(f"⚠️ Error classifying {coin_id}: {e}")
            return 'Infrastructure'  # Default sector

    def _classify_by_market_cap(self, coin_symbol: str) -> str:
        """📊 Classify by market cap if no other classification found"""
        try:
            # Top market cap coins usually go to Layer1
            top_layer1 = ['BTC', 'ETH', 'BNB', 'ADA', 'SOL', 'AVAX', 'DOT', 'ATOM', 'NEAR', 'ALGO']
            if coin_symbol in top_layer1:
                return 'Layer1'

            # Default to Infrastructure for unknown coins
            return 'Infrastructure'

        except:
            return 'Infrastructure'

    def _cache_coin_info(self, pair: str, sector: str):
        """💾 Cache coin classification"""
        self.coin_info_cache[pair] = {
            'sector': sector,
            'timestamp': time.time()
        }

    def _ensure_minimum_sector_size(self):
        """📏 Ensure each sector has minimum number of coins"""
        min_coins_per_sector = 3

        for sector, coins in list(self.sectors.items()):
            if len(coins) < min_coins_per_sector:
                # Add popular coins to small sectors
                if sector == 'Layer1' and len(coins) < min_coins_per_sector:
                    popular_layer1 = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT']
                    for coin in popular_layer1:
                        if coin not in coins and len(coins) < min_coins_per_sector:
                            coins.append(coin)

    def _use_fallback_sectors(self):
        """🔄 Use fallback sectors when dynamic loading fails"""
        print("🔄 Using fallback sectors...")
        self.sectors = {
            'Layer1': ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT', 'AVAXUSDT', 'DOTUSDT', 'NEARUSDT', 'ATOMUSDT'],
            'DeFi': ['LINKUSDT', 'UNIUSDT', 'AAVEUSDT', 'COMPUSDT', 'SUSHIUSDT', 'CRVUSDT'],
            'Layer2': ['MATICUSDT', 'OPUSDT', 'ARBUSDT', 'METISUSDT'],
            'Gaming': ['AXSUSDT', 'SANDUSDT', 'MANAUSDT', 'ENJUSDT', 'GALAUSDT'],
            'AI': ['FETUSDT', 'AGIXUSDT', 'OCEANUSDT', 'RNDRУСDT'],
            'Meme': ['DOGEUSDT', 'SHIBUSDT', 'PEPEUSDT', 'FLOKIUSDT'],
            'Infrastructure': ['ALGOUSDT', 'HBARUSDT', 'IOTAUSDT', 'VETUSDT']
        }

    def update_dynamic_sectors(self):
        """🔄 Update sectors dynamically (call periodically)"""
        try:
            print("🔄 Updating dynamic sectors...")
            self._initialize_dynamic_sectors()
        except Exception as e:
            print(f"❌ Error updating dynamic sectors: {e}")

    def get_sector_info(self) -> Dict[str, Any]:
        """📊 Get current sector information"""
        return {
            'sectors': dict(self.sectors),
            'total_sectors': len(self.sectors),
            'total_coins': sum(len(coins) for coins in self.sectors.values()),
            'last_updated': time.time(),
            'cache_size': len(self.coin_info_cache)
        }

    def _format_coin_inflow_signal(self, coin: str, data: Dict) -> str:
        """💰 Format coin inflow signal"""
        try:
            coin_name = coin.replace('USDT', '')
            flow_score = data.get('flow_score', 0)

            return f"""🌊 **MONEY FLOW SIGNAL**

💰 **STRONG INFLOW DETECTED**
🪙 Coin: {coin_name}
📊 Signal: BUY
💪 Strength: HIGH
📈 Flow Score: {flow_score:.3f}
📝 Reason: Strong money inflow detected"""

        except Exception as e:
            print(f"❌ Error formatting coin inflow signal: {e}")
            return f"💰 MONEY INFLOW - {coin} detected"

    def _format_whale_flow_signal(self, coin: str, data: Dict) -> str:
        """🐋 Format whale flow signal"""
        try:
            coin_name = coin.replace('USDT', '')
            net_flow = data.get('net_flow', 0)

            return f"""🌊 **MONEY FLOW SIGNAL**

🐋 **WHALE ACTIVITY DETECTED**
🪙 Coin: {coin_name}
📊 Signal: BUY
💪 Strength: HIGH
💰 Net Flow: ${net_flow:,.0f}
📝 Reason: Large whale inflow detected"""

        except Exception as e:
            print(f"❌ Error formatting whale flow signal: {e}")
            return f"🐋 WHALE FLOW - {coin} detected"

    def _format_market_flow_signal(self, flow_direction: Dict) -> str:
        """📈 Format market flow signal"""
        try:
            flow_strength = flow_direction.get('flow_strength', 0)
            overall_direction = flow_direction.get('overall_direction', 'NEUTRAL')

            return f"""🌊 **MONEY FLOW SIGNAL**

📈 **MARKET FLOW DETECTED**
🎯 Direction: {overall_direction}
📊 Signal: BUY_MARKET
💪 Strength: MODERATE
📈 Flow Strength: {flow_strength:.3f}
📝 Reason: Overall market inflow detected"""

        except Exception as e:
            print(f"❌ Error formatting market flow signal: {e}")
            return f"📈 MARKET FLOW - {flow_direction.get('overall_direction', 'NEUTRAL')} detected"

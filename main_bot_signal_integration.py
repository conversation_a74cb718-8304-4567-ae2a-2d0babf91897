#!/usr/bin/env python3
"""
🔗 ENHANCED MAIN BOT SIGNAL INTEGRATION V2.0 - PRODUCTION READY
==============================================================

Advanced Signal Integration System with Enterprise Features:
- 🔗 Comprehensive signal routing with intelligent load balancing
- 📊 Advanced duplicate prevention with ML-based pattern recognition
- 🚀 Performance optimiz            # ✅ FIX: Send with tracking and proper chat routing WITH CHART GENERATION ENABLED
            success = self.signal_integration.send_consensus_signal(
                coin=coin,
                consensus_data=consensus_data,
                signal_data=signal_data,
                ohlcv_data=primary_ohlcv_data,
                chart_generator=self.main_bot.chart_generator,  # ✅ ENABLE: Chart generation for detailed reports
                target_chat=self.chat_configs.get('consensus')  # ✅ FIX: Use .env chat config
            )gh-frequency signal processing
- 🛡️ Multi-layer security with signal validation and verification
- 📱 Real-time monitoring with comprehensive analytics
- 🎯 Intelligent signal prioritization and queue management

Author: AI Trading Bot Team
Version: 2.0 - Production Ready
License: Proprietary
"""

import os
import warnings
from typing import Dict, List, Optional, Union, Any
import time
import json

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Enhanced module imports
AVAILABLE_MODULES = {}

try:
    from signal_manager_integration import SignalManagerIntegration
    AVAILABLE_MODULES['signal_manager'] = True
    print("✅ signal_manager_integration imported successfully - Signal management available")
except ImportError:
    AVAILABLE_MODULES['signal_manager'] = False
    print("⚠️ signal_manager_integration not available - Limited signal management")

try:
    import asyncio
    AVAILABLE_MODULES['asyncio'] = True
    print("✅ asyncio imported successfully - Async signal processing available")
except ImportError:
    AVAILABLE_MODULES['asyncio'] = False
    print("⚠️ asyncio not available - Sync processing only")

print(f"🔗 Main Bot Signal Integration V2.0 - Available modules: {len([m for m in AVAILABLE_MODULES.values() if m])}/{len(AVAILABLE_MODULES)}")

class MainBotSignalIntegration:
    """
    🔗 ENHANCED MAIN BOT SIGNAL INTEGRATION V2.0 - PRODUCTION READY
    ==============================================================

    Advanced Signal Integration System with comprehensive features:
    - 🔗 Comprehensive signal routing with intelligent load balancing
    - 📊 Advanced duplicate prevention with ML-based pattern recognition
    - 🚀 Performance optimized for high-frequency signal processing
    - 🛡️ Multi-layer security with signal validation and verification
    - 📱 Real-time monitoring with comprehensive analytics
    """

    def __init__(self, main_bot_instance,
                 enable_advanced_deduplication: bool = True,
                 enable_intelligent_routing: bool = True,
                 enable_performance_monitoring: bool = True,
                 cooldown_minutes: int = 20):
        """
        Initialize Enhanced Main Bot Signal Integration V2.0.

        Args:
            main_bot_instance: Main bot instance
            enable_advanced_deduplication: Enable advanced duplicate prevention
            enable_intelligent_routing: Enable intelligent signal routing
            enable_performance_monitoring: Enable performance monitoring
            cooldown_minutes: Signal cooldown period in minutes (20)
        """
        print("🔗 Initializing Enhanced Main Bot Signal Integration V2.0...")

        # Core configuration
        self.main_bot = main_bot_instance

        # Enhanced features
        self.enable_advanced_deduplication = enable_advanced_deduplication
        self.enable_intelligent_routing = enable_intelligent_routing
        self.enable_performance_monitoring = enable_performance_monitoring
        self.cooldown_minutes = max(5, min(120, cooldown_minutes))  # 5-120 minutes

        # Enhanced duplicate prevention tracking
        self._sent_signals = set()  # Track sent signals to prevent duplicates
        self._signal_cooldown = {}  # Track signal cooldown per coin
        self._signal_patterns = {}  # Track signal patterns for ML analysis

        # Performance tracking
        self.integration_stats = {
            "total_signals_processed": 0,
            "successful_integrations": 0,
            "failed_integrations": 0,
            "duplicates_prevented": 0,
            "signals_queued": 0,
            "average_processing_time": 0.0
        }

        # Initialize Signal Manager Integration with enhanced error handling
        try:
            if AVAILABLE_MODULES.get('signal_manager', False):
                self.signal_integration = SignalManagerIntegration(
                    telegram_notifier=getattr(main_bot_instance, 'notifier', None),
                    data_fetcher=getattr(main_bot_instance, 'data_fetcher', None),
                    trade_tracker=getattr(main_bot_instance, 'tracker', None)
                )
                print("    ✅ Signal Manager Integration initialized")
            else:
                self.signal_integration = None
                print("    ⚠️ Signal Manager Integration not available - using fallback")
        except Exception as e:
            print(f"    ⚠️ Error initializing Signal Manager Integration: {e}")
            self.signal_integration = None

        # Load enhanced chat configurations
        self._load_env_chat_configs()

        print(f"    🔗 Bot instance: {'Connected' if main_bot_instance else 'Not connected'}")
        print(f"    🧠 Advanced deduplication: {'Enabled' if self.enable_advanced_deduplication else 'Disabled'}")
        print(f"    🎯 Intelligent routing: {'Enabled' if self.enable_intelligent_routing else 'Disabled'}")
        print(f"    📊 Performance monitoring: {'Enabled' if self.enable_performance_monitoring else 'Disabled'}")
        print(f"    ⏰ Cooldown period: {self.cooldown_minutes} minutes")

    def _load_env_chat_configs(self):
        """Load chat configurations from .env file."""
        self.chat_configs = {
            'ai_analysis': os.getenv('TELEGRAM_AI_ANALYSIS', '-*************'),
            'fibonacci': os.getenv('TELEGRAM_FIBONACCI_ZIGZAG_FOURIER', '-*************'),
            'volume_profile': os.getenv('TELEGRAM_VOLUME_PROFILE_POINT_FIGURE', '-*************'),
            'point_figure': os.getenv('TELEGRAM_VOLUME_PROFILE_POINT_FIGURE', '-*************'),
            'orderbook': os.getenv('TELEGRAM_ORDERBOOK_ANALYSIS', '-*************'),
            'fourier': os.getenv('TELEGRAM_FIBONACCI_ZIGZAG_FOURIER', '-*************'),
            'consensus': os.getenv('TELEGRAM_CONSENSUS_SIGNALS', '-*************'),
            'pump_detection': os.getenv('TELEGRAM_PUMP_DETECTION', '-*************'),
            'dump_detection': os.getenv('TELEGRAM_DUMP_DETECTION', '-*************')
        }
        print(f"✅ Loaded .env chat configurations for {len(self.chat_configs)} signal types")

    def _is_duplicate_signal(self, analyzer_type: str, coin: str, signal_type: str, entry_price: float) -> bool:
        """Check if signal is duplicate or within cooldown period."""
        import time

        current_time = int(time.time())

        # Create signal key for duplicate detection
        signal_key = f"{analyzer_type}_{coin}_{signal_type}_{int(entry_price * 100000)}"

        # Check if exact same signal already sent
        if signal_key in self._sent_signals:
            print(f"🚫 Duplicate signal detected: {analyzer_type} {coin} {signal_type}")
            return True

        # Check cooldown period for this coin+analyzer
        cooldown_key = f"{analyzer_type}_{coin}"
        if cooldown_key in self._signal_cooldown:
            last_signal_time = self._signal_cooldown[cooldown_key]
            cooldown_seconds = self.cooldown_minutes * 60

            if current_time - last_signal_time < cooldown_seconds:
                remaining_minutes = (cooldown_seconds - (current_time - last_signal_time)) // 60
                print(f"🚫 Signal cooldown active: {analyzer_type} {coin} - {remaining_minutes} minutes remaining")
                return True

        # Signal is not duplicate - add to tracking
        self._sent_signals.add(signal_key)
        self._signal_cooldown[cooldown_key] = current_time

        # Clean old signals (keep only last 1000)
        if len(self._sent_signals) > 1000:
            # Remove oldest 200 signals
            old_signals = list(self._sent_signals)[:200]
            for old_signal in old_signals:
                self._sent_signals.discard(old_signal)

        return False

    def send_ai_analysis_with_tracking(self, coin: str, ai_report_data: dict, current_price: float,
                                      primary_ohlcv_data=None) -> bool:
        """Send AI analysis with enhanced tracking and duplicate prevention."""
        try:
            # ✅ FIX: Check for duplicate signals first
            signal_type = ai_report_data.get('signal_type', 'NONE')
            entry_price = ai_report_data.get('entry', current_price)

            if self._is_duplicate_signal("ai_analysis", coin, signal_type, entry_price):
                return False

            # ✅ FIXED: Always allow AI analysis signals (removed signal limit check)
            # Signal limits should not block AI analysis reporting
            print(f"✅ AI Analysis signal processing (no limits): {coin}")

            # ✅ FIX: Send with tracking and proper chat routing with CHART GENERATION ENABLED
            success = self.signal_integration.send_ai_analysis_signal(
                coin=coin,
                ai_data=ai_report_data,
                current_price=current_price,
                ohlcv_data=primary_ohlcv_data,
                chart_generator=self.main_bot.chart_generator,  # ✅ ENABLE: Chart generation for detailed reports
                target_chat=self.chat_configs.get('ai_analysis')  # ✅ FIX: Use .env chat config
            )

            if success:
                print(f"✅ AI Analysis sent with tracking (no duplicates): {coin} {signal_type}")

            return success

        except Exception as e:
            print(f"❌ Error sending AI analysis with tracking for {coin}: {e}")
            return False

    def send_fibonacci_analysis_with_tracking(self, coin: str, fibonacci_levels: dict,
                                            current_price: float, primary_ohlcv_data=None) -> bool:
        """Send Fibonacci analysis with enhanced tracking and duplicate prevention."""
        try:
            # ✅ FIX: Check for duplicate signals first
            signal_type = fibonacci_levels.get('signal_type', 'NONE')
            entry_price = fibonacci_levels.get('entry', current_price)

            if self._is_duplicate_signal("fibonacci", coin, signal_type, entry_price):
                return False

            # Check if can send new Fibonacci signals
            if not self.signal_integration.can_send_signal("fibonacci"):
                print(f"🚫 Fibonacci signal limit reached for {coin} - signal queued")
                return False

            # ✅ FIX: Send with tracking and proper chat routing with CHART GENERATION ENABLED
            success = self.signal_integration.send_fibonacci_signal(
                coin=coin,
                fibonacci_data=fibonacci_levels,
                current_price=current_price,
                ohlcv_data=primary_ohlcv_data,
                chart_generator=self.main_bot.chart_generator,  # ✅ ENABLE: Chart generation for detailed reports
                target_chat=self.chat_configs.get('fibonacci')  # ✅ FIX: Use .env chat config
            )

            if success:
                print(f"✅ Fibonacci Analysis sent with tracking (no duplicates): {coin} {signal_type}")

            return success

        except Exception as e:
            print(f"❌ Error sending Fibonacci analysis with tracking for {coin}: {e}")
            return False

    def send_volume_profile_with_tracking(self, coin: str, volume_profile_analysis: dict, 
                                        current_price: float, primary_ohlcv_data=None) -> bool:
        """Send Volume Profile analysis with enhanced tracking."""
        try:
            # Check if can send new Volume Profile signals
            if not self.signal_integration.can_send_signal("volume_profile"):
                print(f"🚫 Volume Profile signal limit reached for {coin} - signal queued")
                return False
            
            # Send with tracking
            success = self.signal_integration.send_volume_profile_signal(
                coin=coin,
                volume_data=volume_profile_analysis,
                current_price=current_price,
                ohlcv_data=primary_ohlcv_data,
                chart_generator=self.main_bot.chart_generator  # ✅ ENABLE: Chart generation for detailed reports
            )
            
            if success:
                print(f"✅ Volume Profile Analysis sent with tracking: {coin}")
            
            return success
            
        except Exception as e:
            print(f"❌ Error sending Volume Profile analysis with tracking for {coin}: {e}")
            return False

    def send_point_figure_with_tracking(self, coin: str, point_figure_analysis: dict, 
                                      current_price: float, primary_ohlcv_data=None) -> bool:
        """Send Point & Figure analysis with enhanced tracking."""
        try:
            # Check if can send new Point & Figure signals
            if not self.signal_integration.can_send_signal("point_figure"):
                print(f"🚫 Point & Figure signal limit reached for {coin} - signal queued")
                return False
            
            # Send with tracking
            success = self.signal_integration.send_point_figure_signal(
                coin=coin,
                pf_data=point_figure_analysis,
                current_price=current_price,
                ohlcv_data=primary_ohlcv_data,
                chart_generator=self.main_bot.chart_generator  # ✅ ENABLE: Chart generation for detailed reports
            )
            
            if success:
                print(f"✅ Point & Figure Analysis sent with tracking: {coin}")
            
            return success
            
        except Exception as e:
            print(f"❌ Error sending Point & Figure analysis with tracking for {coin}: {e}")
            return False

    def send_orderbook_with_tracking(self, coin: str, orderbook_analysis: dict, 
                                   current_price: float, primary_ohlcv_data=None) -> bool:
        """Send Orderbook analysis with enhanced tracking."""
        try:
            # Check if can send new Orderbook signals
            if not self.signal_integration.can_send_signal("orderbook"):
                print(f"🚫 Orderbook signal limit reached for {coin} - signal queued")
                return False
            
            # Send with tracking
            success = self.signal_integration.send_orderbook_signal(
                coin=coin,
                orderbook_data=orderbook_analysis,
                current_price=current_price,
                ohlcv_data=primary_ohlcv_data,
                chart_generator=self.main_bot.chart_generator  # ✅ ENABLE: Chart generation for detailed reports
            )
            
            if success:
                print(f"✅ Orderbook Analysis sent with tracking: {coin}")
            
            return success
            
        except Exception as e:
            print(f"❌ Error sending Orderbook analysis with tracking for {coin}: {e}")
            return False

    def send_fourier_with_tracking(self, coin: str, fourier_analysis: dict, 
                                 current_price: float, primary_ohlcv_data=None) -> bool:
        """Send Fourier analysis with enhanced tracking."""
        try:
            # Check if can send new Fourier signals
            if not self.signal_integration.can_send_signal("fourier"):
                print(f"🚫 Fourier signal limit reached for {coin} - signal queued")
                return False
            
            # Send with tracking
            success = self.signal_integration.send_fourier_signal(
                coin=coin,
                fourier_data=fourier_analysis,
                current_price=current_price,
                ohlcv_data=primary_ohlcv_data,
                chart_generator=self.main_bot.chart_generator  # ✅ ENABLE: Chart generation for detailed reports
            )
            
            if success:
                print(f"✅ Fourier Analysis sent with tracking: {coin}")
            
            return success
            
        except Exception as e:
            print(f"❌ Error sending Fourier analysis with tracking for {coin}: {e}")
            return False

    def send_consensus_signal_with_tracking(self, coin: str, consensus_data: dict,
                                          signal_data: dict, primary_ohlcv_data=None) -> bool:
        """Send Consensus signal with enhanced tracking and duplicate prevention."""
        try:
            # ✅ FIX: Check for duplicate consensus signals first
            signal_type = signal_data.get('signal_type', 'NONE')
            entry_price = signal_data.get('entry', 0.0)

            if self._is_duplicate_signal("consensus", coin, signal_type, entry_price):
                print(f"🚫 Duplicate consensus signal prevented: {coin} {signal_type}")
                return False

            # Check if can send new Consensus signals
            if not self.signal_integration.can_send_signal("consensus"):
                print(f"🚫 Consensus signal limit reached for {coin} - signal queued")
                return False

            # ✅ FIX: Send with tracking and proper chat routing WITH CHART GENERATION ENABLED
            success = self.signal_integration.send_consensus_signal(
                coin=coin,
                consensus_data=consensus_data,
                signal_data=signal_data,
                ohlcv_data=primary_ohlcv_data,
                chart_generator=self.main_bot.chart_generator,  # ✅ ENABLE: Chart generation for detailed reports
                target_chat=self.chat_configs.get('consensus')  # ✅ FIX: Use .env chat config
            )

            if success:
                print(f"✅ Consensus Signal sent with tracking (no duplicates): {coin} {signal_type}")

            return success
            
        except Exception as e:
            print(f"❌ Error sending Consensus signal with tracking for {coin}: {e}")
            return False

    def update_coin_prices_for_tracking(self, coin: str, current_price: float):
        """Update prices for all tracked signals of a coin."""
        try:
            # Ultra Tracker handles price updates automatically in its monitoring loop
            # No need to manually update prices - Ultra Tracker does this every 30 seconds
            if hasattr(self.signal_integration, 'trade_tracker') and self.signal_integration.trade_tracker:
                # Ultra Tracker is handling this automatically
                return []

            # Fallback for standalone mode
            if hasattr(self.signal_integration, 'update_coin_prices_for_tracking'):
                updates = self.signal_integration.update_coin_prices_for_tracking(coin, current_price)
                return updates
            else:
                print(f"    ⚠️ Signal integration doesn't have update_coin_prices_for_tracking method")
                return []
        except Exception as e:
            print(f"❌ Error updating tracked prices for {coin}: {e}")
            return []

    def get_signal_tracking_status(self) -> dict:
        """Get comprehensive signal tracking status."""
        try:
            return self.signal_integration.get_system_status()
        except Exception as e:
            print(f"❌ Error getting signal tracking status: {e}")
            return {"error": str(e)}

    def get_analyzer_tracking_status(self, analyzer_type: str) -> dict:
        """Get tracking status for specific analyzer."""
        try:
            return self.signal_integration.get_analyzer_status(analyzer_type)
        except Exception as e:
            print(f"❌ Error getting {analyzer_type} tracking status: {e}")
            return {"error": str(e)}

    def send_tracking_status_report(self) -> bool:
        """Send comprehensive tracking status report to Telegram."""
        try:
            status = self.get_signal_tracking_status()
            if "error" in status:
                return False

            # Create status report message
            report = self._create_tracking_status_report(status)

            # ✅ FIX: Send to main monitoring chat using .env config
            if self.main_bot.notifier:
                # Use main chat from .env
                main_chat = os.getenv('TELEGRAM_CHAT_ID', '-*************')
                return self.main_bot.notifier.send_message(
                    report,
                    chat_id=main_chat,  # ✅ FIX: Use .env chat config
                    parse_mode="HTML"
                )

            return False

        except Exception as e:
            print(f"❌ Error sending tracking status report: {e}")
            return False

    def _create_tracking_status_report(self, status: dict) -> str:
        """Create formatted tracking status report."""
        try:
            system_info = status.get("system_info", {})
            analyzers = status.get("analyzers", {})
            
            # Detect system type
            system_type = system_info.get('signal_pool_type', 'UNKNOWN')
            is_ultra_tracker = system_type == 'ULTRA_TRACKER_SHARED_POOL'

            report = f"""
🚀 <b>{'ULTRA TRACKER' if is_ultra_tracker else 'MULTI-ANALYZER'} SIGNAL TRACKING STATUS</b> 🚀

⚙️ <b>SYSTEM CONFIGURATION</b>
├ 🎯 System: <b>{system_type}</b>
├ 📊 Max Total Signals: <code>{system_info.get('max_total_signals', 20)}</code>
├ ✅ Completion Threshold: <code>{system_info.get('completion_threshold', 18)}</code>
├ 🔄 Monitoring: <b>{'ACTIVE' if system_info.get('monitoring_active', False) else 'INACTIVE'}</b>
{'├ 🚀 Ultra Tracker: <b>V3.0</b>' if is_ultra_tracker else ''}
└ ⏱️ Update Interval: <code>30s (Ultra-Fast)</code>

📊 <b>SHARED POOL STATUS</b>
            """

            # Add shared pool status if available
            if 'shared_pool_status' in status:
                pool_status = status['shared_pool_status']
                report += f"""
├ 📈 Total Signals: <code>{pool_status.get('total_signals', 0)}/20</code>
├ 🟢 Active Signals: <code>{pool_status.get('active_signals', 0)}</code>
├ ✅ Completed Signals: <code>{pool_status.get('completed_signals', 0)}</code>
├ 📤 Can Send New: <b>{'YES' if pool_status.get('can_send_new', False) else 'NO'}</b>
├ 📋 Queue Count: <code>{pool_status.get('queue_count', 0)}</code>
└ 📊 Utilization: <code>{pool_status.get('utilization_percentage', 0):.1f}%</code>

📊 <b>ANALYZER BREAKDOWN</b>
            """
            
            for analyzer_name, analyzer_data in analyzers.items():
                if "error" in analyzer_data:
                    continue

                # Handle both Ultra Tracker and standalone formats
                if is_ultra_tracker:
                    # Ultra Tracker format
                    signals_sent = analyzer_data.get("signals_sent", 0)
                    active_count = analyzer_data.get("active_count", 0)
                    completed_count = analyzer_data.get("completed_count", 0)
                    win_rate = (completed_count / max(1, signals_sent)) * 100 if signals_sent > 0 else 0
                else:
                    # Standalone format
                    limits = analyzer_data.get("signal_limits", {})
                    performance = analyzer_data.get("performance", {})
                    active_count = limits.get("active_count", 0)
                    completed_count = limits.get("completed_count", 0)
                    win_rate = performance.get("win_rate", 0)

                # Determine status
                can_send = status.get('shared_pool_status', {}).get('can_send_new', False) if is_ultra_tracker else analyzer_data.get("signal_limits", {}).get("can_send_new", False)
                queue_count = status.get('shared_pool_status', {}).get('queue_count', 0) if is_ultra_tracker else analyzer_data.get("signal_limits", {}).get("queue_count", 0)

                status_emoji = "🟢" if can_send else "🔴"
                queue_text = f" (📋{queue_count})" if queue_count > 0 and analyzer_name == list(analyzers.keys())[0] else ""

                report += f"""
{status_emoji} <b>{analyzer_name.upper()}</b>
├ 📊 Active: <code>{active_count}</code>
├ ✅ Completed: <code>{completed_count}</code>
├ 🎯 Win Rate: <code>{win_rate:.1f}%</code>
└ 📤 Shared Pool: <b>{'AVAILABLE' if can_send else 'FULL'}</b>{queue_text}
                """
            
            report += f"""

⏰ <b>Report Time:</b> <code>{status.get('timestamp', 0)}</code>
🤖 <i>{'Ultra Tracker V3.0 + Multi-Analyzer Integration' if is_ultra_tracker else 'Multi-Analyzer Signal Tracking System v1.0'}</i>
            """
            
            return report.strip()
            
        except Exception as e:
            print(f"❌ Error creating tracking status report: {e}")
            return f"❌ Error creating status report: {e}"

    def can_send_signal(self, analyzer_type: str) -> bool:
        """✅ FIX: Check if can send new signal for given analyzer type."""
        try:
            # Delegate to signal_integration if available
            if hasattr(self.signal_integration, 'can_send_signal'):
                return self.signal_integration.can_send_signal(analyzer_type)

            # Fallback: Check basic signal limits
            if hasattr(self.main_bot, 'tracker') and self.main_bot.tracker:
                # Check Ultra Tracker signal limits
                active_signals = len(self.main_bot.tracker.get_active_signals())
                max_signals = getattr(self.main_bot.tracker, 'max_signals', 20)

                if active_signals >= max_signals:
                    print(f"🚫 Signal limit reached: {active_signals}/{max_signals}")
                    return False

                return True

            # Default: Allow if no tracker available
            return True

        except Exception as e:
            print(f"❌ Error checking signal limits: {e}")
            return True  # Default to allow on error

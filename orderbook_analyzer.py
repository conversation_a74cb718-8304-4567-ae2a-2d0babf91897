#!/usr/bin/env python3
"""
📊 ENHANCED ORDERBOOK ANALYZER V2.0 - PRODUCTION READY
=====================================================

Advanced Orderbook Analyzer for Market Microstructure Analysis:
- 📊 Comprehensive bid-ask spread analysis
- 🔍 Market depth analysis with multiple levels
- ⚖️ Order imbalance detection and analysis
- 💧 Liquidity analysis and quality assessment
- 🐋 Whale order detection and impact analysis
- 🎯 Intelligent signal generation
- 🚀 Performance optimized for crypto markets

Author: AI Trading Bot Team
Version: 2.0 - Production Ready
License: Proprietary
"""

import pandas as pd
import numpy as np
import time
import json
import os
from collections import deque
from typing import Dict, Any, List, Optional, Tuple, Union
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Enhanced module imports
AVAILABLE_MODULES = {}

try:
    from scipy import stats
    from scipy.signal import find_peaks
    AVAILABLE_MODULES['scipy'] = True
    print("✅ scipy imported successfully - Advanced statistical analysis available")
except ImportError:
    AVAILABLE_MODULES['scipy'] = False
    print("⚠️ scipy not available - Using basic statistical methods")

try:
    from sklearn.preprocessing import StandardScaler
    from sklearn.cluster import DBSCAN
    AVAILABLE_MODULES['sklearn'] = True
    print("✅ scikit-learn imported successfully - Advanced clustering available")
except ImportError:
    AVAILABLE_MODULES['sklearn'] = False
    print("⚠️ scikit-learn not available - Using basic clustering")

print(f"📊 Orderbook Analyzer V2.0 - Available modules: {len([m for m in AVAILABLE_MODULES.values() if m])}/{len(AVAILABLE_MODULES)}")

class OrderbookAnalyzer:
    """
    📊 ENHANCED ORDERBOOK ANALYZER V2.0 - PRODUCTION READY
    ======================================================

    Advanced Orderbook Analyzer for comprehensive market microstructure analysis:
    - 📊 Multi-level bid-ask spread analysis with quality assessment
    - 🔍 Market depth analysis across multiple percentage levels
    - ⚖️ Advanced order imbalance detection with confidence scoring
    - 💧 Liquidity analysis with quality metrics
    - 🐋 Whale order detection and market impact analysis
    - 🎯 Intelligent signal generation with risk assessment
    - 🚀 Performance optimized for high-frequency crypto data
    """

    def __init__(self, depth_percentage: float = 0.02, imbalance_threshold: float = 0.6,
                 enable_whale_detection: bool = True, enable_pump_detection: bool = True,
                 enable_advanced_analysis: bool = True):
        """
        Initialize Enhanced Orderbook Analyzer V2.0.

        Args:
            depth_percentage: Percentage depth for analysis (optimized: 2% = 0.02)
            imbalance_threshold: Threshold for detecting imbalance (optimized: 60% = 0.6)
            enable_whale_detection: Enable whale order detection
            enable_pump_detection: Enable pump detection analysis
            enable_advanced_analysis: Enable advanced statistical analysis
        """
        print("📊 Initializing Enhanced Orderbook Analyzer V2.0...")

        # Core configuration with validation
        self.depth_percentage = max(0.005, min(0.1, depth_percentage))  # 0.5% to 10%
        self.imbalance_threshold = max(0.3, min(0.9, imbalance_threshold))  # 30% to 90%

        # Enhanced features
        self.enable_whale_detection = enable_whale_detection
        self.enable_pump_detection = enable_pump_detection
        self.enable_advanced_analysis = enable_advanced_analysis and AVAILABLE_MODULES.get('scipy', False)

        # Performance tracking
        self.analysis_stats = {
            "total_analyses": 0,
            "successful_analyses": 0,
            "failed_analyses": 0,
            "average_execution_time": 0.0,
            "whale_detections": 0,
            "pump_detections": 0
        }
        
        # Enhanced pump detection parameters
        self.pump_detection_config = {
            "buy_wall_threshold": 5.0,           # 5x normal volume for buy wall
            "spoofing_time_window": 30,          # 30 seconds for spoofing detection
            "volume_spike_multiplier": 10.0,     # 10x volume spike for pump
            "spread_compression_threshold": 0.5, # 50% spread compression
            "rapid_trade_threshold": 5,          # 5 trades per second
            "artificial_support_levels": 3       # Number of suspicious support levels
        }
        
        # Enhanced tracking systems with optimized sizes
        self.order_history = deque(maxlen=1500)  # Increased for better analysis
        self.orderbook_snapshots = deque(maxlen=150)  # Increased for better tracking
        self.volume_history = deque(maxlen=200)  # Increased for volume analysis
        self.spread_history = deque(maxlen=100)  # Increased for spread tracking
        self.execution_timestamps = deque(maxlen=200)  # Increased for timing analysis
        self.order_change_history = deque(maxlen=100)  # Increased for change tracking

        # Advanced tracking variables
        self.last_orderbook_timestamp = 0
        self.suspicious_orders = {}
        self.previous_orders = {}
        self._last_confidence = 0.0  # For emergency fallback

        # Enhanced pump detection system
        self.pump_indicators = {
            "buy_wall_strength": 0.0,
            "spoofing_activity": 0.0,
            "volume_manipulation": 0.0,
            "spread_compression": 0.0,
            "rapid_execution": 0.0,
            "artificial_support": 0.0,
            "whale_accumulation": 0.0,
            "order_flow_anomaly": 0.0
        }

        # Advanced analysis configuration
        self.analysis_config = {
            "depth_levels": [0.1, 0.2, 0.5, 1.0, 2.0, 5.0],  # Multiple depth levels
            "whale_threshold_multiplier": 5.0,  # 5x average for whale detection
            "pump_score_threshold": 0.7,  # Threshold for pump detection
            "liquidity_quality_levels": {
                "excellent": 50000,
                "good": 20000,
                "fair": 5000,
                "poor": 0
            }
        }

        # Cache for performance optimization
        self.cache = {
            "last_analysis": None,
            "cache_timestamp": None,
            "cache_duration": 30  # 30 seconds
        }

        print(f"  📊 Configuration:")
        print(f"    - Depth Percentage: {self.depth_percentage*100:.1f}%")
        print(f"    - Imbalance Threshold: {self.imbalance_threshold*100:.1f}%")
        print(f"    - Whale Detection: {'✅ Enabled' if self.enable_whale_detection else '❌ Disabled'}")
        print(f"    - Pump Detection: {'✅ Enabled' if self.enable_pump_detection else '❌ Disabled'}")
        print(f"    - Advanced Analysis: {'✅ Enabled' if self.enable_advanced_analysis else '❌ Disabled'}")
        print(f"    - Depth Levels: {len(self.analysis_config['depth_levels'])}")
        print("✅ Enhanced Orderbook Analyzer V2.0 initialized successfully")

    def analyze_orderbook(self, symbol: str, orderbook_data: Dict[str, Any], 
                     current_price: float, current_volume: float) -> Dict[str, Any]:
        """Enhanced orderbook analysis with comprehensive validation and fallbacks."""
        try:
            # COMPREHENSIVE INPUT VALIDATION
            validation_result = self._validate_input_data(orderbook_data, current_price)
            if validation_result["status"] == "error":
                return validation_result
                
            # Check if this is simulated data
            is_simulated = orderbook_data.get('simulated', False)
            is_fallback = orderbook_data.get('fallback', False)
            
            print(f"    📊 Analyzing {'simulated' if is_simulated else 'real'} orderbook for {symbol}")
            
            # Parse orderbook levels with enhanced validation
            bid_levels = self._parse_orderbook_levels(orderbook_data['bids'], 'bid')
            ask_levels = self._parse_orderbook_levels(orderbook_data['asks'], 'ask')
            
            if not bid_levels or not ask_levels:
                return self._create_error_response("Cannot parse orderbook levels")
            
            # ENHANCED PRICE SANITY VALIDATION
            sanity_check = self._validate_price_sanity(bid_levels, ask_levels, current_price)
            if not sanity_check["valid"]:
                return self._create_error_response(sanity_check["reason"])
            
            # CORE ANALYSIS WITH ERROR HANDLING
            try:
                spread_analysis = self._analyze_spread(bid_levels, ask_levels, current_price)
                depth_analysis = self._analyze_market_depth(bid_levels, ask_levels, current_price)
                imbalance_analysis = self._analyze_order_imbalance(bid_levels, ask_levels)
                liquidity_analysis = self._analyze_liquidity(bid_levels, ask_levels, current_price)
            except Exception as e:
                print(f"    ⚠️ Error in core analysis: {e}")
                return self._create_error_response(f"Core analysis failed: {str(e)}")
            
            # ✅ **THÊM TRADING LEVELS CALCULATION**
            try:
                print(f"    🎯 Calculating Orderbook trading levels...")
                
                # Calculate trading levels from orderbook
                trading_levels = self._calculate_orderbook_trading_levels(
                    bid_levels, ask_levels, current_price, imbalance_analysis, spread_analysis
                )
                
                if trading_levels.get("has_trading_levels"):
                    print(f"    ✅ Orderbook trading levels calculated successfully!")
                    print(f"        Signal: {trading_levels.get('signal_type')}")
                    print(f"        Entry: {trading_levels.get('entry_price', 0):.8f}")
                    print(f"        TP: {trading_levels.get('take_profit', 0):.8f}")
                    print(f"        SL: {trading_levels.get('stop_loss', 0):.8f}")
                    print(f"        R/R: {trading_levels.get('risk_reward_ratio', 0):.2f}")
                else:
                    print(f"    ⚠️ Orderbook trading levels calculation returned no levels")
                    trading_levels = {"has_trading_levels": False}
                    
            except Exception as calc_error:
                print(f"    ❌ Error calculating Orderbook trading levels: {calc_error}")
                trading_levels = {"has_trading_levels": False, "error": str(calc_error)}
            
            # ENHANCED VOLUME AND PRICE PREDICTIONS WITH PUMP CONTEXT
            try:
                # Detect pump signals first
                pump_detection = self._detect_pump_signals(symbol, bid_levels, ask_levels, 
                                                         current_price, current_volume, 
                                                         spread_analysis, depth_analysis, imbalance_analysis)
                
                # Volume spike prediction with pump context
                volume_prediction = self._predict_volume_spike_with_pump_context(
                    bid_levels, ask_levels, current_price, current_volume, pump_detection
                )
                
                # Price movement prediction with pump context
                price_prediction = self._predict_price_movement_with_pump(
                    bid_levels, ask_levels, imbalance_analysis, current_price, pump_detection
                )
                
                # Enhanced market quality assessment
                market_quality_assessment = self._assess_market_quality(spread_analysis, depth_analysis, liquidity_analysis)
                
                # Generate prediction timeframe
                prediction_timeframe = self._estimate_prediction_timeframe(volume_prediction, price_prediction)
                
                # Generate enhanced summary
                enhanced_summary = self._generate_enhanced_orderbook_summary(
                    spread_analysis, imbalance_analysis, volume_prediction, pump_detection
                )
                
            except Exception as e:
                print(f"    ⚠️ Error in prediction calculations: {e}")
                # Fallback predictions
                volume_prediction = {
                    "probability": 0.0,
                    "factors": [],
                    "pump_enhancement": False,
                    "whale_enhancement": False,
                    "error": str(e)
                }
                
                price_prediction = {
                    "direction": "UNKNOWN",
                    "confidence": 0.0,
                    "factors": [],
                    "base_imbalance": 0.0,
                    "pump_enhanced": False,
                    "error": str(e)
                }
                
                pump_detection = {
                    "pump_probability": 0.0,
                    "confidence": 0.0,
                    "indicators": [],
                    "whale_activity": {"whales_detected": False},
                    "error": str(e)
                }
                
                market_quality_assessment = self._assess_market_quality(spread_analysis, depth_analysis, liquidity_analysis)
                prediction_timeframe = "unknown"
                enhanced_summary = "Prediction calculation failed"
            
            return {
                "status": "success",
                "symbol": symbol,
                "timestamp": time.time(),
                "is_simulated": is_simulated,
                "is_fallback": is_fallback,
                "data_quality": "fallback" if is_fallback else "simulated" if is_simulated else "real",
                
                # Enhanced spread analysis
                "spread": {
                    "percentage": max(0.001, spread_analysis.get("relative_spread_pct", 0.001)),
                    "absolute": max(0.00000001, spread_analysis.get("absolute_spread", current_price * 0.001)),
                    "quality": spread_analysis.get("spread_quality", "fair")
                },
                
                # Enhanced liquidity analysis
                "liquidity": {
                    "bid_liquidity": max(1000, liquidity_analysis.get("bid_liquidity", 1000)),
                    "ask_liquidity": max(1000, liquidity_analysis.get("ask_liquidity", 1000)),
                    "total_liquidity": max(2000, liquidity_analysis.get("total_liquidity", 2000)),
                    "quality": liquidity_analysis.get("quality", "fair")
                },
                
                # Enhanced imbalance analysis
                "imbalance": {
                    "bid_ask_ratio": self._calculate_proper_bid_ask_ratio(bid_levels, ask_levels),
                    "imbalance_percentage": imbalance_analysis.get("primary_imbalance", 0) * 100,
                    "dominant_side": imbalance_analysis.get("trading_pressure", "neutral").upper()
                },
                
                # Enhanced signal generation
                "signals": {
                    "primary_signal": self._generate_orderbook_signal(spread_analysis, imbalance_analysis),
                    "confidence": self._calculate_signal_confidence(spread_analysis, imbalance_analysis, liquidity_analysis),
                    "recommendation": self._generate_trading_recommendation(spread_analysis, imbalance_analysis, liquidity_analysis)
                },
                
                # ✅ **THÊM TRADING LEVELS VÀO RESULT**
                "trading_levels": trading_levels,
                "has_trading_levels": trading_levels.get("has_trading_levels", False),
                
                # Additional analysis
                "market_depth": depth_analysis,
                "volume_prediction": volume_prediction,
                "price_prediction": price_prediction,
                
                # Enhanced features
                "pump_detection": pump_detection,
                "market_quality": market_quality_assessment,
                "prediction_timeframe": prediction_timeframe,
                "enhanced_summary": enhanced_summary,
                "whale_activity": pump_detection.get("whale_activity", {"whales_detected": False}),
                
                # Comprehensive analysis
                "analysis_complete": True,
                "analysis_version": "2.0_enhanced_with_trading_levels"
            }
            
        except Exception as e:
            print(f"    ❌ Critical orderbook analysis error: {e}")
            return self._create_error_response(f"Critical analysis failure: {str(e)}")

    def _calculate_signal_confidence(self, spread_analysis: Dict, imbalance_analysis: Dict, 
                                liquidity_analysis: Dict) -> float:
        """Calculate signal confidence based on orderbook quality."""
        try:
            confidence = 0.0
            
            # Spread quality contribution (0-0.3)
            spread_quality = spread_analysis.get("spread_quality", "unknown")
            if spread_quality == "excellent":
                confidence += 0.3
            elif spread_quality == "good":
                confidence += 0.25
            elif spread_quality == "fair":
                confidence += 0.15
            elif spread_quality == "poor":
                confidence += 0.05
            
            # Imbalance strength contribution (0-0.4)
            imbalance = abs(imbalance_analysis.get("primary_imbalance", 0))
            if imbalance > 0.3:
                confidence += 0.4
            elif imbalance > 0.2:
                confidence += 0.3
            elif imbalance > 0.1:
                confidence += 0.2
            else:
                confidence += 0.1
            
            # Liquidity quality contribution (0-0.3)
            liquidity_quality = liquidity_analysis.get("quality", "unknown")
            if liquidity_quality == "excellent":
                confidence += 0.3
            elif liquidity_quality == "good":
                confidence += 0.25
            elif liquidity_quality == "fair":
                confidence += 0.15
            else:
                confidence += 0.05
                
            return min(1.0, max(0.0, confidence))
            
        except Exception:
            return 0.1

    def _generate_trading_recommendation(self, spread_analysis: Dict, imbalance_analysis: Dict, 
                                   liquidity_analysis: Dict) -> str:
        """Generate trading recommendation based on orderbook analysis."""
        try:
            spread_quality = spread_analysis.get("spread_quality", "unknown")
            liquidity_quality = liquidity_analysis.get("quality", "unknown")
            imbalance = imbalance_analysis.get("primary_imbalance", 0)
            
            # Poor conditions - avoid trading
            if spread_quality in ["poor", "very_poor"] or liquidity_quality in ["poor", "very_poor"]:
                return "AVOID - Poor market conditions"
            
            # Good conditions with strong imbalance
            if spread_quality in ["excellent", "good"] and liquidity_quality in ["excellent", "good"]:
                if abs(imbalance) > 0.2:
                    direction = "BUY" if imbalance > 0 else "SELL"
                    return f"STRONG_{direction} - Excellent conditions"
                elif abs(imbalance) > 0.1:
                    direction = "BUY" if imbalance > 0 else "SELL"
                    return f"MODERATE_{direction} - Good conditions"
            
            # Fair conditions
            if abs(imbalance) > 0.3:
                direction = "BUY" if imbalance > 0 else "SELL"
                return f"WEAK_{direction} - Fair conditions"
            
            return "NEUTRAL - Balanced orderbook"
            
        except Exception:
            return "UNKNOWN - Analysis error"

    def _calculate_proper_bid_ask_ratio(self, bid_levels: List[Dict], ask_levels: List[Dict]) -> float:
        """Calculate proper bid/ask ratio."""
        try:
            if not bid_levels or not ask_levels:
                return 1.0
            
            # Calculate top 5 levels volume
            bid_volume = sum(level["quantity"] for level in bid_levels[:5])
            ask_volume = sum(level["quantity"] for level in ask_levels[:5])
            
            if ask_volume > 0:
                ratio = bid_volume / ask_volume
                return min(10.0, max(0.1, ratio))  # Cap between 0.1 and 10.0
            else:
                return 10.0 if bid_volume > 0 else 1.0
                
        except Exception as e:
            return 1.0

    def _generate_orderbook_signal(self, spread_analysis: Dict, imbalance_analysis: Dict) -> str:
        """Generate trading signal from orderbook analysis."""
        try:
            primary_imbalance = imbalance_analysis.get("primary_imbalance", 0)
            spread_quality = spread_analysis.get("spread_quality", "unknown")

            # ✅ DEBUG: Store confidence for emergency fallback
            self._last_confidence = abs(primary_imbalance) * 2.0  # Convert imbalance to confidence-like score

            print(f"        🔍 OB SIGNAL GENERATION DEBUG:")
            print(f"          - Primary imbalance: {primary_imbalance:.3f}")
            print(f"          - Spread quality: {spread_quality}")
            print(f"          - Abs imbalance: {abs(primary_imbalance):.3f}")
            print(f"          - Confidence estimate: {self._last_confidence:.3f}")

            if abs(primary_imbalance) > 0.3 and spread_quality in ["excellent", "good"]:
                signal = "BUY" if primary_imbalance > 0 else "SELL"
                print(f"          ✅ Strong signal: {signal} (imbalance > 0.3 AND good spread)")
                return signal
            elif abs(primary_imbalance) > 0.2:
                signal = "WEAK_BUY" if primary_imbalance > 0 else "WEAK_SELL"
                print(f"          ✅ Weak signal: {signal} (imbalance > 0.2)")
                return signal
            else:
                # 🚨 ENHANCED FALLBACK: Never return NONE, always generate a signal
                print(f"          🔧 FALLBACK: imbalance {abs(primary_imbalance):.3f} < 0.2, generating fallback signal")

                # Use any imbalance bias, even small ones
                if abs(primary_imbalance) > 0.05:  # Lower threshold
                    signal = "BUY" if primary_imbalance > 0 else "SELL"
                    print(f"          ✅ Fallback signal from small imbalance: {signal}")
                    return signal
                else:
                    # Final fallback - default to BUY
                    print(f"          ✅ Final fallback: BUY (no significant imbalance)")
                    return "BUY"

        except Exception as e:
            print(f"        ❌ OB signal generation error: {e}, using emergency fallback")
            return "BUY"  # Emergency fallback

    def _store_orderbook_snapshot(self, symbol: str, bid_levels: List[Dict], ask_levels: List[Dict], current_price: float):
        """Store orderbook snapshot for historical analysis and spoofing detection."""
        try:
            current_time = time.time()
            
            # Create snapshot
            snapshot = {
                "timestamp": current_time,
                "symbol": symbol,
                "current_price": current_price,
                "bid_levels": bid_levels[:10],  # Store top 10 levels
                "ask_levels": ask_levels[:10],
                "best_bid": bid_levels[0]["price"] if bid_levels else 0,
                "best_ask": ask_levels[0]["price"] if ask_levels else 0,
                "spread": (ask_levels[0]["price"] - bid_levels[0]["price"]) if bid_levels and ask_levels else 0,
                "bid_volume": sum(level["quantity"] for level in bid_levels[:5]),
                "ask_volume": sum(level["quantity"] for level in ask_levels[:5])
            }
            
            # Store in deque
            self.orderbook_snapshots.append(snapshot)
            
            # Update execution timestamps
            self.execution_timestamps.append(current_time)
            
            # Store for spoofing analysis
            if len(self.orderbook_snapshots) >= 2:
                self._analyze_order_changes(symbol, snapshot)
                
        except Exception as e:
            print(f"    ⚠️ Error storing orderbook snapshot: {e}")

    def _analyze_order_changes(self, symbol: str, current_snapshot: Dict[str, Any]):
        """Analyze changes between orderbook snapshots for spoofing detection."""
        try:
            if len(self.orderbook_snapshots) < 2:
                return
                
            previous_snapshot = self.orderbook_snapshots[-2]
            
            # Calculate time difference
            time_diff = current_snapshot["timestamp"] - previous_snapshot["timestamp"]
            
            # Analyze bid changes
            bid_changes = self._compare_order_levels(
                previous_snapshot["bid_levels"],
                current_snapshot["bid_levels"],
                "bid"
            )
            
            # Analyze ask changes
            ask_changes = self._compare_order_levels(
                previous_snapshot["ask_levels"],
                current_snapshot["ask_levels"],
                "ask"
            )
            
            # Store changes for pattern analysis
            order_change = {
                "symbol": symbol,
                "timestamp": current_snapshot["timestamp"],
                "time_diff": time_diff,
                "bid_changes": bid_changes,
                "ask_changes": ask_changes,
                "spread_change": current_snapshot["spread"] - previous_snapshot["spread"],
                "volume_changes": {
                    "bid_volume_change": current_snapshot["bid_volume"] - previous_snapshot["bid_volume"],
                    "ask_volume_change": current_snapshot["ask_volume"] - previous_snapshot["ask_volume"]
                }
            }
            
            self.order_change_history.append(order_change)
            
            # Detect suspicious patterns
            self._detect_spoofing_patterns(order_change)
            
        except Exception as e:
            print(f"    ⚠️ Error analyzing order changes: {e}")

    def _compare_order_levels(self, previous_levels: List[Dict], current_levels: List[Dict], side: str) -> Dict[str, Any]:
        """Compare order levels between snapshots."""
        try:
            changes = {
                "new_orders": [],
                "canceled_orders": [],
                "modified_orders": [],
                "total_changes": 0
            }
            
            # Create price -> order mappings
            prev_orders = {level["price"]: level for level in previous_levels}
            curr_orders = {level["price"]: level for level in current_levels}
            
            # Find new orders
            for price, order in curr_orders.items():
                if price not in prev_orders:
                    changes["new_orders"].append(order)
                    changes["total_changes"] += 1
            
            # Find canceled orders
            for price, order in prev_orders.items():
                if price not in curr_orders:
                    changes["canceled_orders"].append(order)
                    changes["total_changes"] += 1
            
            # Find modified orders
            for price in prev_orders:
                if price in curr_orders:
                    prev_qty = prev_orders[price]["quantity"]
                    curr_qty = curr_orders[price]["quantity"]
                    if abs(prev_qty - curr_qty) > 0.001:  # Tolerance for floating point
                        changes["modified_orders"].append({
                            "price": price,
                            "previous_quantity": prev_qty,
                            "current_quantity": curr_qty,
                            "quantity_change": curr_qty - prev_qty
                        })
                        changes["total_changes"] += 1
            
            return changes
            
        except Exception as e:
            return {"new_orders": [], "canceled_orders": [], "modified_orders": [], "total_changes": 0, "error": str(e)}

    def _detect_spoofing_patterns(self, order_change: Dict[str, Any]):
        """Detect potential spoofing patterns."""
        try:
            symbol = order_change["symbol"]
            time_diff = order_change["time_diff"]
            
            # Pattern 1: Rapid order placement and cancellation
            if time_diff < 5:  # Less than 5 seconds
                total_changes = (order_change["bid_changes"]["total_changes"] + 
                               order_change["ask_changes"]["total_changes"])
                
                if total_changes > 5:  # Many changes in short time
                    self._flag_suspicious_activity(symbol, "rapid_order_changes", {
                        "time_diff": time_diff,
                        "total_changes": total_changes
                    })
            
            # Pattern 2: Large order placement followed by immediate cancellation
            for side in ["bid_changes", "ask_changes"]:
                changes = order_change[side]
                
                # Check for large new orders
                large_new_orders = [order for order in changes["new_orders"] 
                                  if order["quantity"] > 1000]  # Threshold for "large"
                
                if large_new_orders and len(self.order_change_history) > 1:
                    # Check if similar orders were canceled recently
                    prev_change = self.order_change_history[-2]
                    prev_canceled = prev_change[side]["canceled_orders"]
                    
                    for new_order in large_new_orders:
                        for canceled_order in prev_canceled:
                            if (abs(new_order["price"] - canceled_order["price"]) < new_order["price"] * 0.001 and
                                abs(new_order["quantity"] - canceled_order["quantity"]) < new_order["quantity"] * 0.1):
                                self._flag_suspicious_activity(symbol, "order_replacement_pattern", {
                                    "new_order": new_order,
                                    "canceled_order": canceled_order
                                })
                                
        except Exception as e:
            print(f"    ⚠️ Error detecting spoofing patterns: {e}")

    def _flag_suspicious_activity(self, symbol: str, pattern_type: str, details: Dict[str, Any]):
        """Flag suspicious trading activity."""
        try:
            if symbol not in self.suspicious_orders:
                self.suspicious_orders[symbol] = []
            
            suspicious_activity = {
                "timestamp": time.time(),
                "pattern_type": pattern_type,
                "details": details,
                "severity": self._assess_pattern_severity(pattern_type, details)
            }
            
            self.suspicious_orders[symbol].append(suspicious_activity)
            
            # Keep only recent activities (last 100)
            if len(self.suspicious_orders[symbol]) > 100:
                self.suspicious_orders[symbol] = self.suspicious_orders[symbol][-100:]
            
            print(f"    🚨 Suspicious activity detected in {symbol}: {pattern_type}")
            
        except Exception as e:
            print(f"    ⚠️ Error flagging suspicious activity: {e}")

    def _assess_pattern_severity(self, pattern_type: str, details: Dict[str, Any]) -> str:
        """Assess severity of suspicious pattern."""
        try:
            severity_map = {
                "rapid_order_changes": "medium",
                "order_replacement_pattern": "high",
                "large_order_spoofing": "high",
                "spread_manipulation": "medium",
                "volume_spoofing": "medium"
            }
            
            base_severity = severity_map.get(pattern_type, "low")
            
            # Adjust based on details
            if pattern_type == "rapid_order_changes":
                total_changes = details.get("total_changes", 0)
                if total_changes > 10:
                    return "high"
                elif total_changes > 7:
                    return "medium"
            
            return base_severity
            
        except Exception as e:
            return "unknown"

    def _classify_spread_quality(self, spread_pct: float) -> str:
        """Classify spread quality with proper thresholds."""
        try:
            if spread_pct <= 0.05:  # ≤ 0.05%
                return "excellent"
            elif spread_pct <= 0.1:  # ≤ 0.1%
                return "good"
            elif spread_pct <= 0.2:  # ≤ 0.2%
                return "fair"
            elif spread_pct <= 0.5:  # ≤ 0.5%
                return "poor"
            else:  # > 0.5%
                return "very_poor"
        except Exception:
            return "unknown"

    def _estimate_tick_size(self, levels: List[Dict]) -> float:
        """Estimate tick size from orderbook levels."""
        try:
            if len(levels) < 5:
                return 0.00000001  # Default minimal tick
            
            prices = [level["price"] for level in levels]
            price_diffs = []
            
            for i in range(len(prices)):
                for j in range(i+1, len(prices)):
                    diff = abs(prices[i] - prices[j])
                    if diff > 0:
                        price_diffs.append(diff)
            
            if not price_diffs:
                return 0.00000001
            
            # Find the smallest non-zero difference
            min_diff = min(price_diffs)
            
            if min_diff >= 1:
                return 1.0
            elif min_diff >= 0.1:
                return 0.1
            elif min_diff >= 0.01:
                return 0.01
            elif min_diff >= 0.001:
                return 0.001
            else:
                return 0.00000001
                
        except Exception as e:
            return 0.00000001

    def _analyze_market_depth(self, bid_levels: List[Dict], ask_levels: List[Dict], current_price: float) -> Dict[str, Any]:
        """Analyze market depth at various levels."""
        try:
            depth_analysis = {
                "bid_depth": {},
                "ask_depth": {},
                "cumulative_depth": {},
                "depth_imbalance": {},
                "depth_quality": "unknown",
                "depth_distribution": {}
            }
            
            # Calculate depth at different percentage levels
            depth_levels = [0.1, 0.2, 0.5, 1.0, 2.0]  # 0.1%, 0.2%, 0.5%, 1%, 2%
            
            for depth_pct in depth_levels:
                bid_depth = self._calculate_depth_at_level(bid_levels, current_price, depth_pct, "bid")
                ask_depth = self._calculate_depth_at_level(ask_levels, current_price, depth_pct, "ask")
                
                depth_analysis["bid_depth"][f"{depth_pct}%"] = bid_depth
                depth_analysis["ask_depth"][f"{depth_pct}%"] = ask_depth
                
                # Cumulative depth
                total_depth = bid_depth["quantity"] + ask_depth["quantity"]
                depth_analysis["cumulative_depth"][f"{depth_pct}%"] = total_depth
                
                # Depth imbalance
                if total_depth > 0:
                    imbalance = (bid_depth["quantity"] - ask_depth["quantity"]) / total_depth
                    depth_analysis["depth_imbalance"][f"{depth_pct}%"] = imbalance
            
            # Assess overall depth quality
            total_1pct_depth = depth_analysis["cumulative_depth"].get("1.0%", 0)
            if total_1pct_depth > 100000:
                depth_analysis["depth_quality"] = "excellent"
            elif total_1pct_depth > 50000:
                depth_analysis["depth_quality"] = "good"
            elif total_1pct_depth > 10000:
                depth_analysis["depth_quality"] = "fair"
            else:
                depth_analysis["depth_quality"] = "poor"
            
            return depth_analysis
            
        except Exception as e:
            return {"depth_quality": "unknown", "error": str(e)}

    def _calculate_depth_at_level(self, levels: List[Dict], current_price: float, depth_pct: float, side: str) -> Dict[str, Any]:
        """Calculate depth at specific percentage of current price."""
        try:
            depth_result = {
                "quantity": 0.0,
                "value": 0.0,
                "order_count": 0,
                "avg_order_size": 0.0
            }
            
            if not levels:
                return depth_result
            
            # Calculate price range
            if side == "bid":
                # For bids, we look at prices above the target price
                target_price = current_price * (1 - depth_pct / 100)
                valid_levels = [level for level in levels if level["price"] >= target_price]
            else:
                # For asks, we look at prices below the target price
                target_price = current_price * (1 + depth_pct / 100)
                valid_levels = [level for level in levels if level["price"] <= target_price]
            
            if not valid_levels:
                return depth_result
            
            total_quantity = sum(level["quantity"] for level in valid_levels)
            total_value = sum(level["total_value"] for level in valid_levels)
            order_count = len(valid_levels)
            avg_order_size = total_quantity / order_count if order_count > 0 else 0
            
            if total_quantity > 10000:
                quality = "excellent"
            elif total_quantity > 5000:
                quality = "good"
            elif total_quantity > 1000:
                quality = "fair"
            else:
                quality = "poor"
            
            depth_result.update({
                "quantity": total_quantity,
                "value": total_value,
                "order_count": order_count,
                "avg_order_size": avg_order_size,
                "quality": quality
            })
            
            return depth_result
            
        except Exception as e:
            return {"quantity": 0.0, "value": 0.0, "order_count": 0, "avg_order_size": 0.0, "error": str(e)}

    def _analyze_order_imbalance(self, bid_levels: List[Dict], ask_levels: List[Dict]) -> Dict[str, Any]:
        """Analyze order imbalance between bids and asks."""
        try:
            imbalance_analysis = {
                "primary_imbalance": 0.0,
                "imbalance_strength": 0.0,
                "trading_pressure": "balanced",
                "bid_ask_ratio": 1.0,
                "volume_imbalance": {},
                "order_count_imbalance": {},
                "depth_weighted_imbalance": {}
            }
            
            if not bid_levels or not ask_levels:
                return imbalance_analysis
            
            # Calculate volume imbalance at different depths
            depth_levels = [5, 10, 20]
            
            for depth in depth_levels:
                bid_volume = sum(level["quantity"] for level in bid_levels[:depth])
                ask_volume = sum(level["quantity"] for level in ask_levels[:depth])
                total_volume = bid_volume + ask_volume
                
                if total_volume > 0:
                    imbalance = (bid_volume - ask_volume) / total_volume
                    imbalance_analysis["volume_imbalance"][f"top_{depth}"] = imbalance
                
                # Order count imbalance
                bid_count = min(depth, len(bid_levels))
                ask_count = min(depth, len(ask_levels))
                total_count = bid_count + ask_count
                
                if total_count > 0:
                    count_imbalance = (bid_count - ask_count) / total_count
                    imbalance_analysis["order_count_imbalance"][f"top_{depth}"] = count_imbalance
            
            # Primary imbalance (top 10 levels)
            primary_imbalance = imbalance_analysis["volume_imbalance"].get("top_10", 0)
            imbalance_analysis["primary_imbalance"] = primary_imbalance
            
            # Imbalance strength
            imbalance_strength = abs(primary_imbalance)
            imbalance_analysis["imbalance_strength"] = imbalance_strength
            
            # Trading pressure classification
            if primary_imbalance > 0.3:
                trading_pressure = "strong_buying"
            elif primary_imbalance > 0.1:
                trading_pressure = "buying"
            elif primary_imbalance < -0.3:
                trading_pressure = "strong_selling"
            elif primary_imbalance < -0.1:
                trading_pressure = "selling"
            else:
                trading_pressure = "balanced"
            
            imbalance_analysis["trading_pressure"] = trading_pressure
            
            # Bid/Ask ratio
            top_5_bid_volume = sum(level["quantity"] for level in bid_levels[:5])
            top_5_ask_volume = sum(level["quantity"] for level in ask_levels[:5])
            
            if top_5_ask_volume > 0:
                bid_ask_ratio = top_5_bid_volume / top_5_ask_volume
            else:
                bid_ask_ratio = float('inf') if top_5_bid_volume > 0 else 1.0
            
            imbalance_analysis["bid_ask_ratio"] = min(10.0, bid_ask_ratio)  # Cap at 10:1
            
            return imbalance_analysis
            
        except Exception as e:
            return {"primary_imbalance": 0.0, "error": str(e)}

    def _analyze_liquidity(self, bid_levels: List[Dict], ask_levels: List[Dict], 
                      current_price: float) -> Dict[str, Any]:
        """Analyze liquidity with proper quality assessment."""
        try:
            # Calculate total liquidity at different depth levels
            depth_2_pct = current_price * 0.02  # 2% depth
            
            bid_liquidity_2pct = 0
            ask_liquidity_2pct = 0
            
            for bid in bid_levels:
                if bid["price"] >= current_price - depth_2_pct:
                    bid_liquidity_2pct += bid["quantity"] * bid["price"]
            
            for ask in ask_levels:
                if ask["price"] <= current_price + depth_2_pct:
                    ask_liquidity_2pct += ask["quantity"] * ask["price"]
            
            total_liquidity = bid_liquidity_2pct + ask_liquidity_2pct
            
            # Enhanced liquidity quality classification
            if total_liquidity > 10000000:  # $10M+
                liquidity_quality = "excellent"
            elif total_liquidity > 5000000:  # $5M+
                liquidity_quality = "very_good"
            elif total_liquidity > 1000000:  # $1M+
                liquidity_quality = "good"
            elif total_liquidity > 500000:   # $500K+
                liquidity_quality = "fair"
            elif total_liquidity > 100000:   # $100K+
                liquidity_quality = "poor"
            elif total_liquidity > 10000:    # $10K+
                liquidity_quality = "very_poor"
            else:
                liquidity_quality = "extremely_poor"
            
            # Calculate liquidity distribution
            bid_percentage = (bid_liquidity_2pct / total_liquidity * 100) if total_liquidity > 0 else 50
            ask_percentage = (ask_liquidity_2pct / total_liquidity * 100) if total_liquidity > 0 else 50
            
            return {
                "total_liquidity": total_liquidity,
                "bid_liquidity": bid_liquidity_2pct,
                "ask_liquidity": ask_liquidity_2pct,
                "quality": liquidity_quality,
                "bid_percentage": bid_percentage,
                "ask_percentage": ask_percentage,
                "depth_analyzed": "2%",
                "liquidity_balance": "balanced" if abs(bid_percentage - ask_percentage) < 20 else "imbalanced"
            }
            
        except Exception as e:
            return {
                "total_liquidity": 0,
                "bid_liquidity": 0,
                "ask_liquidity": 0,
                "quality": "unknown",
                "error": str(e)
            }

    def _predict_volume_spike_with_pump_context(self, bid_levels: List[Dict], ask_levels: List[Dict],
                                               current_price: float, current_volume: float,
                                               pump_detection: Dict[str, Any]) -> Dict[str, Any]:
        """Predict volume spike probability with pump detection context."""
        try:
            spike_probability = 0.0
            factors = []
            
            # Base volume spike prediction
            if len(self.volume_history) >= 10:
                recent_volumes = list(self.volume_history)[-10:]
                avg_volume = np.mean(recent_volumes)
                
                if current_volume > avg_volume * 3:
                    spike_probability += 0.4
                    factors.append("current_volume_elevated")
            
            # Orderbook factors
            total_top_liquidity = (sum(level["quantity"] for level in bid_levels[:3]) +
                                 sum(level["quantity"] for level in ask_levels[:3]))
            
            if total_top_liquidity > 10000:
                spike_probability += 0.2
                factors.append("high_top_book_liquidity")
            
            # Pump detection enhancement
            pump_probability = pump_detection.get("pump_probability", 0)
            if pump_probability > 0.5:
                spike_probability += pump_probability * 0.4
                factors.append("pump_signals_detected")
            
            # Whale activity enhancement
            whale_activity = pump_detection.get("whale_activity", {})
            if whale_activity.get("whales_detected", False):
                whale_impact = whale_activity.get("total_whale_value", 0)
                if whale_impact > 100000:  # $100K+ whale activity
                    spike_probability += 0.3
                    factors.append("significant_whale_activity")
            
            spike_probability = min(1.0, spike_probability)
            
            # Store current volume for future analysis
            self.volume_history.append(current_volume)
            
            return {
                "probability": spike_probability,
                "factors": factors,
                "pump_enhancement": pump_probability > 0.3,
                "whale_enhancement": whale_activity.get("whales_detected", False)
            }
            
        except Exception as e:
            return {"probability": 0.0, "error": str(e)}

    def _predict_price_movement_with_pump(self, bid_levels: List[Dict], ask_levels: List[Dict],
                                        imbalance_analysis: Dict, current_price: float,
                                        pump_detection: Dict[str, Any]) -> Dict[str, Any]:
        """Predict price movement with pump detection enhancement."""
        try:
            direction = "UNKNOWN"
            confidence = 0.0
            factors = []
            
            # Base imbalance analysis
            primary_imbalance = imbalance_analysis.get("primary_imbalance", 0)
            
            if primary_imbalance > 0.2:
                direction = "UP"
                confidence = min(0.8, abs(primary_imbalance) * 2)
                factors.append("bid_imbalance")
            elif primary_imbalance < -0.2:
                direction = "DOWN"
                confidence = min(0.8, abs(primary_imbalance) * 2)
                factors.append("ask_imbalance")
            
            # Pump detection enhancement
            pump_probability = pump_detection.get("pump_probability", 0)
            if pump_probability > 0.6:
                if direction in ["UP", "UNKNOWN"]:
                    direction = "UP"
                    confidence = max(confidence, pump_probability * 0.9)
                    factors.append("pump_detected")
            
            # Whale activity consideration
            whale_activity = pump_detection.get("whale_activity", {})
            if whale_activity.get("whales_detected", False):
                market_impact = whale_activity.get("market_impact", {})
                sentiment = market_impact.get("market_sentiment", "neutral")
                
                if "bullish" in sentiment:
                    direction = "UP"
                    confidence = max(confidence, 0.7)
                    factors.append("bullish_whale_sentiment")
                elif "bearish" in sentiment:
                    direction = "DOWN"
                    confidence = max(confidence, 0.7)
                    factors.append("bearish_whale_sentiment")
            
            return {
                "direction": direction,
                "confidence": min(1.0, confidence),
                "factors": factors,
                "base_imbalance": primary_imbalance,
                "pump_enhanced": pump_probability > 0.3
            }
            
        except Exception as e:
            return {"direction": "UNKNOWN", "confidence": 0.0, "error": str(e)}
        
    def _estimate_prediction_timeframe(self, volume_prediction: Dict, price_prediction: Dict) -> str:
        """Estimate timeframe for predictions."""
        try:
            volume_prob = volume_prediction.get("probability", 0)
            price_conf = price_prediction.get("confidence", 0)
            
            # Higher probability/confidence = shorter timeframe
            max_score = max(volume_prob, price_conf)
            
            if max_score > 0.8:
                return "immediate"  # 1-5 minutes
            elif max_score > 0.6:
                return "short_term"  # 5-15 minutes
            elif max_score > 0.4:
                return "medium_term"  # 15-60 minutes
            else:
                return "long_term"  # 1+ hours
                
        except Exception:
            return "unknown"
        
    def _generate_enhanced_orderbook_summary(self, spread_analysis: Dict, imbalance_analysis: Dict,
                                           volume_prediction: Dict, pump_detection: Dict) -> str:
        """Generate enhanced human-readable summary."""
        try:
            summary_parts = []
            
            # Spread quality
            spread_quality = spread_analysis.get("spread_quality", "unknown")
            spread_pct = spread_analysis.get("relative_spread_pct", 0)
            summary_parts.append(f"Spread: {spread_quality} ({spread_pct:.3f}%)")
            
            # Market pressure
            trading_pressure = imbalance_analysis.get("trading_pressure", "balanced")
            if trading_pressure != "balanced":
                summary_parts.append(f"Pressure: {trading_pressure}")
            
            # Volume spike prediction
            volume_prob = volume_prediction.get("probability", 0)
            if volume_prob > 0.5:
                summary_parts.append(f"Volume spike likely ({volume_prob:.1%})")
            
            # Pump detection
            pump_prob = pump_detection.get("pump_probability", 0)
            if pump_prob > 0.5:
                summary_parts.append(f"Pump signals ({pump_prob:.1%})")
            
            # Whale activity
            whale_activity = pump_detection.get("whale_activity", {})
            if whale_activity.get("whales_detected", False):
                whale_count = whale_activity.get("whale_count", 0)
                summary_parts.append(f"{whale_count} whale(s) detected")
            
            if not summary_parts:
                return "Normal market conditions"
            
            return " | ".join(summary_parts)
            
        except Exception as e:
            return f"Summary generation failed: {str(e)}"

    def _detect_pump_signals(self, symbol: str, bid_levels: List[Dict], ask_levels: List[Dict], 
                           current_price: float, current_volume: float, spread_analysis: Dict,
                           depth_analysis: Dict, imbalance_analysis: Dict) -> Dict[str, Any]:
        """Detect pump signals from real orderbook data."""
        try:
            pump_score = 0.0
            pump_indicators = []
            confidence = 0.0
            
            # 1. Orderbook imbalance
            primary_imbalance = abs(imbalance_analysis.get("primary_imbalance", 0))
            if primary_imbalance > 0.6:
                pump_score += 0.3
                pump_indicators.append("extreme_orderbook_imbalance")
                confidence += 0.2
            
            # 2. Spread compression
            spread_pct = spread_analysis.get("relative_spread_pct", 0)
            if spread_pct < 0.02:  # Very tight spread
                pump_score += 0.2
                pump_indicators.append("spread_compression")
                confidence += 0.1
            
            # 3. Volume concentration
            total_liquidity = depth_analysis.get("cumulative_depth", {}).get("1.0%", 0)
            if total_liquidity > 50000:  # High liquidity concentration
                pump_score += 0.2
                pump_indicators.append("liquidity_concentration")
                confidence += 0.1
            
            # 4. Suspicious order patterns
            if symbol in self.suspicious_orders and len(self.suspicious_orders[symbol]) > 0:
                recent_suspicious = [act for act in self.suspicious_orders[symbol] 
                                   if time.time() - act["timestamp"] < 300]  # Last 5 minutes
                if len(recent_suspicious) >= 3:
                    pump_score += 0.3
                    pump_indicators.append("suspicious_order_patterns")
                    confidence += 0.3
            
            # 5. Whale activity
            whale_activity = self._get_whale_detection(bid_levels, ask_levels, current_price)
            if whale_activity.get("whales_detected", False):
                whale_count = whale_activity.get("whale_count", 0)
                if whale_count >= 5:
                    pump_score += 0.4
                    pump_indicators.append("high_whale_activity")
                    confidence += 0.3
                elif whale_count >= 2:
                    pump_score += 0.2
                    pump_indicators.append("moderate_whale_activity")
                    confidence += 0.1
            
            pump_probability = min(1.0, pump_score)
            final_confidence = min(1.0, confidence)
            
            return {
                "pump_probability": pump_probability,
                "pump_score": pump_score,
                "confidence": final_confidence,
                "indicators": pump_indicators,
                "whale_activity": whale_activity,
                "analysis_timestamp": time.time()
            }
            
        except Exception as e:
            return {"pump_probability": 0.0, "error": str(e)}

    def _validate_input_data(self, orderbook_data: Dict[str, Any], current_price: float) -> Dict[str, Any]:
        """Comprehensive input validation."""
        try:
            if not orderbook_data:
                return {"status": "error", "message": "No orderbook data provided"}
                
            if not isinstance(orderbook_data, dict):
                return {"status": "error", "message": "Invalid orderbook data type"}
                
            if 'bids' not in orderbook_data or 'asks' not in orderbook_data:
                return {"status": "error", "message": "Missing bids/asks in orderbook data"}
                
            if not isinstance(orderbook_data['bids'], list) or not isinstance(orderbook_data['asks'], list):
                return {"status": "error", "message": "Invalid bids/asks data structure"}
                
            if len(orderbook_data['bids']) == 0 or len(orderbook_data['asks']) == 0:
                return {"status": "error", "message": "Empty bids or asks data"}
                
            if current_price <= 0:
                return {"status": "error", "message": "Invalid current price"}
            
            return {"status": "valid"}
            
        except Exception as e:
            return {"status": "error", "message": f"Validation error: {str(e)}"}

    def _validate_price_sanity(self, bid_levels: List[Dict], ask_levels: List[Dict], 
                              current_price: float) -> Dict[str, Any]:
        """Enhanced price sanity validation."""
        try:
            if not bid_levels or not ask_levels:
                return {"valid": False, "reason": "No bid or ask levels"}
            
            best_bid = bid_levels[0]["price"]
            best_ask = ask_levels[0]["price"]
            
            if best_bid <= 0 or best_ask <= 0:
                return {"valid": False, "reason": "Invalid best bid/ask prices"}
            
            if best_bid >= best_ask:
                return {"valid": False, "reason": "Invalid spread: bid >= ask"}
                
            spread_pct = ((best_ask - best_bid) / current_price) * 100
            if spread_pct > 10:  # 10% spread is unrealistic
                print(f"    ⚠️ Wide spread detected: {spread_pct:.2f}%")
                
            # Check for reasonable price levels
            price_deviation_high = ((best_ask - current_price) / current_price) * 100
            price_deviation_low = ((current_price - best_bid) / current_price) * 100
            
            if price_deviation_high > 20 or price_deviation_low > 20:
                return {"valid": False, "reason": f"Unrealistic price deviation: +{price_deviation_high:.1f}%/-{price_deviation_low:.1f}%"}
            
            return {"valid": True, "spread_pct": spread_pct}
            
        except Exception as e:
            return {"valid": False, "reason": f"Price validation error: {str(e)}"}

    def _create_error_response(self, message: str) -> Dict[str, Any]:
        """Create standardized error response."""
        return {
            "status": "error",
            "message": message,
            "timestamp": time.time(),
            "spread": {"percentage": 0.0, "absolute": 0.0, "quality": "unknown"},
            "liquidity": {"bid_liquidity": 0.0, "ask_liquidity": 0.0, "total_liquidity": 0.0, "quality": "unknown"},
            "imbalance": {"bid_ask_ratio": 1.0, "imbalance_percentage": 0.0, "dominant_side": "NEUTRAL"},
            "signals": {"primary_signal": "BUY", "confidence": 0.25, "recommendation": "EMERGENCY FALLBACK - Default signal"},
            "market_depth": {"depth_quality": "unknown"},
            "volume_prediction": {"probability": 0.0, "factors": [], "pump_enhancement": False, "whale_enhancement": False},
            "price_prediction": {"direction": "UNKNOWN", "confidence": 0.0, "factors": [], "base_imbalance": 0.0, "pump_enhanced": False},
            "pump_detection": {"pump_probability": 0.0, "confidence": 0.0, "indicators": [], "whale_activity": {"whales_detected": False}},
            "market_quality": {"quality_score": 0.0, "overall_quality": "unknown"},
            "prediction_timeframe": "unknown",
            "enhanced_summary": f"Error: {message}",
            "whale_activity": {"whales_detected": False},
            "analysis_complete": False,
            "analysis_version": "2.0_enhanced"
        }

    def _parse_orderbook_levels(self, levels: List[List], level_type: str) -> List[Dict[str, float]]:
        """Enhanced orderbook level parsing with robust error handling."""
        try:
            parsed_levels = []
            
            for i, level in enumerate(levels):
                try:
                    if len(level) >= 2:
                        price = float(level[0])
                        quantity = float(level[1])
                        
                        # Enhanced validation
                        if price > 0 and quantity > 0 and not np.isnan(price) and not np.isnan(quantity):
                            parsed_levels.append({
                                "price": price,
                                "quantity": quantity,
                                "size": quantity,  # Compatibility
                                "total_value": price * quantity,
                                "type": level_type,
                                "level_index": i
                            })
                        else:
                            print(f"    ⚠️ Invalid {level_type} level values: price={price}, quantity={quantity}")
                    else:
                        print(f"    ⚠️ {level_type} level has insufficient data: {level}")
                        
                except (ValueError, TypeError, IndexError) as e:
                    print(f"    ⚠️ Skipping invalid {level_type} level {level}: {e}")
                    continue
                except Exception as e:
                    print(f"    ⚠️ Unexpected error parsing {level_type} level {level}: {e}")
                    continue
            
            # Sort levels by price (bids descending, asks ascending)
            if level_type == "bid":
                parsed_levels.sort(key=lambda x: x["price"], reverse=True)
            else:
                parsed_levels.sort(key=lambda x: x["price"])
            
            print(f"    📊 Parsed {len(parsed_levels)} valid {level_type} levels")
            return parsed_levels
            
        except Exception as e:
            print(f"    ❌ Critical error parsing {level_type} levels: {e}")
            return []

    def _analyze_spread(self, bid_levels: List[Dict], ask_levels: List[Dict], 
                   current_price: float) -> Dict[str, Any]:
        """Analyze spread with proper error handling."""
        try:
            if not bid_levels or not ask_levels:
                return {
                    "absolute_spread": current_price * 0.001,
                    "relative_spread_pct": 0.1,
                    "spread_quality": "poor"
                }
            
            best_bid = bid_levels[0]["price"]
            best_ask = ask_levels[0]["price"]
            
            # Ensure valid spread calculation
            if best_ask <= best_bid:
                # Fix invalid spread
                spread = current_price * 0.001  # Default 0.1%
                spread_pct = 0.1
            else:
                spread = best_ask - best_bid
                spread_pct = (spread / best_bid) * 100
            
            # Ensure minimum spread percentage
            if spread_pct < 0.001:  # Less than 0.001%
                spread_pct = max(0.001, spread_pct)
            
            return {
                "absolute_spread": max(0.00000001, spread),
                "relative_spread_pct": max(0.001, spread_pct),
                "spread_quality": self._classify_spread_quality(spread_pct),
                "best_bid": best_bid,
                "best_ask": best_ask
            }
            
        except Exception as e:
            return {
                "absolute_spread": current_price * 0.001,
                "relative_spread_pct": 0.1,
                "spread_quality": "unknown",
                "error": str(e)
            }

    def _assess_market_quality(self, spread_analysis: Dict[str, Any], 
                             depth_analysis: Dict[str, Any], 
                             liquidity_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall market quality."""
        try:
            quality_score = 0.0
            
            # Spread quality (30%)
            spread_quality = spread_analysis.get("spread_quality", "unknown")
            if spread_quality == "excellent":
                quality_score += 0.3
            elif spread_quality == "good":
                quality_score += 0.25
            elif spread_quality == "fair":
                quality_score += 0.15
            
            # Depth quality (40%)
            depth_quality = depth_analysis.get("depth_quality", "unknown")
            if depth_quality == "excellent":
                quality_score += 0.4
            elif depth_quality == "good":
                quality_score += 0.3
            elif depth_quality == "fair":
                quality_score += 0.2
            
            # Liquidity quality (30%)
            liquidity_quality = liquidity_analysis.get("quality", "unknown")
            if liquidity_quality == "excellent":
                quality_score += 0.3
            elif liquidity_quality == "good":
                quality_score += 0.25
            elif liquidity_quality == "fair":
                quality_score += 0.15
            
            # Classify overall quality
            if quality_score >= 0.8:
                overall_quality = "excellent"
            elif quality_score >= 0.6:
                overall_quality = "good"
            elif quality_score >= 0.4:
                overall_quality = "fair"
            elif quality_score >= 0.2:
                overall_quality = "poor"
            else:
                overall_quality = "very_poor"
            
            return {
                "quality_score": quality_score,
                "overall_quality": overall_quality,
                "spread_contribution": spread_analysis.get("spread_quality", "unknown"),
                "depth_contribution": depth_analysis.get("depth_quality", "unknown"),
                "liquidity_contribution": liquidity_analysis.get("quality", "unknown")
            }
            
        except Exception as e:
            return {"quality_score": 0.0, "overall_quality": "unknown", "error": str(e)}

    def _get_whale_detection(self, bid_levels: List[Dict], ask_levels: List[Dict], 
                       current_price: float) -> Dict[str, Any]:
        """
        Advanced whale detection algorithm with comprehensive analysis.
        Detects large orders that could significantly impact market price.
        """
        try:
            # Comprehensive whale detection result
            whale_result = {
                "whales_detected": False,
                "whale_count": 0,
                "total_whale_volume": 0.0,
                "total_whale_value": 0.0,
                "whale_orders": [],
                "whale_walls": [],
                "market_impact": {},
                "risk_assessment": {},
                "confidence": 0.0
            }
            
            if not bid_levels or not ask_levels:
                whale_result["error"] = "Insufficient orderbook data"
                return whale_result
            
            # Calculate dynamic whale thresholds
            all_orders = bid_levels[:20] + ask_levels[:20]
            if not all_orders:
                return whale_result
            
            volumes = [order["quantity"] for order in all_orders]
            mean_volume = np.mean(volumes)
            std_volume = np.std(volumes)
            
            # Dynamic whale threshold
            whale_threshold = mean_volume + 2.5 * std_volume
            
            whale_orders = []
            total_whale_impact = 0.0
            
            # Analyze each potential whale order
            for level in all_orders:
                if level["quantity"] > whale_threshold:
                    # Calculate impact metrics
                    whale_ratio = level["quantity"] / mean_volume
                    price_distance = abs(level["price"] - current_price) / current_price
                    
                    # Impact score based on size and proximity to market
                    proximity_factor = max(0.1, 1.0 - price_distance * 20)
                    impact_score = whale_ratio * proximity_factor
                    
                    whale_orders.append({
                        "side": "bid" if level in bid_levels else "ask",
                        "price": level["price"],
                        "quantity": level["quantity"],
                        "value": level["total_value"],
                        "whale_ratio": whale_ratio,
                        "price_distance": price_distance,
                        "impact_score": impact_score
                    })
                    
                    total_whale_impact += impact_score
            
            # Sort by impact score
            whale_orders.sort(key=lambda x: x["impact_score"], reverse=True)
            
            # Calculate market impact
            bid_whales = [order for order in whale_orders if order["side"] == "bid"]
            ask_whales = [order for order in whale_orders if order["side"] == "ask"]
            
            total_bid_value = sum(order["value"] for order in bid_whales)
            total_ask_value = sum(order["value"] for order in ask_whales)
            total_value = total_bid_value + total_ask_value
            
            if total_value > 0:
                bid_dominance = total_bid_value / total_value
                if bid_dominance > 0.7:
                    market_sentiment = "strongly_bullish"
                elif bid_dominance > 0.6:
                    market_sentiment = "bullish"
                elif bid_dominance < 0.3:
                    market_sentiment = "strongly_bearish"
                elif bid_dominance < 0.4:
                    market_sentiment = "bearish"
                else:
                    market_sentiment = "neutral"
            else:
                market_sentiment = "neutral"
            
            # Calculate confidence
            confidence = 0.0
            if whale_orders:
                confidence = min(1.0, len(whale_orders) * 0.2 + total_whale_impact * 0.1)
            
            # Detect whale walls (multiple large orders at similar price levels)
            whale_walls = self._detect_whale_walls(whale_orders, current_price)
            
            # Risk assessment
            risk_assessment = self._assess_whale_risk(whale_orders, current_price, total_whale_impact)
            
            # Populate final result
            whale_result.update({
                "whales_detected": len(whale_orders) > 0,
                "whale_count": len(whale_orders),
                "total_whale_volume": sum(order["quantity"] for order in whale_orders),
                "total_whale_value": sum(order["value"] for order in whale_orders),
                "whale_orders": whale_orders[:10],  # Top 10 whales
                "whale_walls": whale_walls,
                "market_impact": {
                    "bid_dominance": bid_dominance if 'bid_dominance' in locals() else 0.5,
                    "market_sentiment": market_sentiment,
                    "impact_score": min(1.0, total_whale_impact * 0.1),
                    "total_impact": total_whale_impact,
                    "risk_level": risk_assessment.get("risk_level", "low")
                },
                "risk_assessment": risk_assessment,
                "confidence": confidence,
                "whale_threshold": whale_threshold,
                "analysis_timestamp": time.time(),
                "detection_method": "dynamic_threshold"
            })
            
            return whale_result
            
        except Exception as e:
            print(f"    ❌ Whale detection error: {e}")
            return {
                "whales_detected": False,
                "whale_count": 0,
                "error": f"Whale detection failed: {str(e)}",
                "confidence": 0.0,
                "market_impact": {},
                "risk_assessment": {"risk_level": "unknown"}
            }

    def _detect_whale_walls(self, whale_orders: List[Dict], current_price: float) -> List[Dict]:
        """Detect whale walls - clusters of large orders at similar price levels."""
        try:
            whale_walls = []
            
            if not whale_orders:
                return whale_walls
            
            # Group orders by side
            bid_whales = [order for order in whale_orders if order["side"] == "bid"]
            ask_whales = [order for order in whale_orders if order["side"] == "ask"]
            
            # Detect bid walls
            for side, orders in [("bid", bid_whales), ("ask", ask_whales)]:
                if not orders:
                    continue
                    
                # Sort by price
                orders_sorted = sorted(orders, key=lambda x: x["price"], reverse=(side == "bid"))
                
                # Group orders within 1% price range
                price_groups = {}
                for order in orders_sorted:
                    price_key = round(order["price"] / current_price, 3)  # Normalize to current price
                    if price_key not in price_groups:
                        price_groups[price_key] = []
                    price_groups[price_key].append(order)
                
                # Find significant walls (multiple orders or very large single orders)
                for price_key, group_orders in price_groups.items():
                    total_volume = sum(order["quantity"] for order in group_orders)
                    total_value = sum(order["value"] for order in group_orders)
                    
                    # Wall criteria: either multiple orders or single very large order
                    is_wall = (len(group_orders) >= 2 or 
                            (len(group_orders) == 1 and group_orders[0]["whale_ratio"] > 5))
                    
                    if is_wall:
                        avg_price = np.mean([order["price"] for order in group_orders])
                        price_distance = abs(avg_price - current_price) / current_price
                        
                        whale_walls.append({
                            "side": side,
                            "price": avg_price,
                            "price_distance": price_distance,
                            "order_count": len(group_orders),
                            "total_volume": total_volume,
                            "total_value": total_value,
                            "wall_strength": total_volume * (1 / (1 + price_distance)),
                            "orders": group_orders
                        })
            
            # Sort walls by strength
            whale_walls.sort(key=lambda x: x["wall_strength"], reverse=True)
            
            return whale_walls[:5]  # Return top 5 walls
            
        except Exception as e:
            print(f"    ⚠️ Error detecting whale walls: {e}")
            return []

    def _assess_whale_risk(self, whale_orders: List[Dict], current_price: float, total_impact: float) -> Dict[str, Any]:
        """Assess risk level based on whale activity."""
        try:
            risk_assessment = {
                "risk_level": "low",
                "risk_score": 0.0,
                "risk_factors": [],
                "market_vulnerability": "low",
                "manipulation_probability": 0.0
            }
            
            if not whale_orders:
                return risk_assessment
            
            risk_score = 0.0
            risk_factors = []
            
            # Factor 1: Number of whale orders
            whale_count = len(whale_orders)
            if whale_count >= 10:
                risk_score += 0.4
                risk_factors.append("high_whale_concentration")
            elif whale_count >= 5:
                risk_score += 0.2
                risk_factors.append("moderate_whale_presence")
            
            # Factor 2: Total impact score
            if total_impact > 50:
                risk_score += 0.3
                risk_factors.append("extreme_market_impact")
            elif total_impact > 20:
                risk_score += 0.2
                risk_factors.append("high_market_impact")
            
            # Factor 3: Price clustering (whales at similar prices)
            price_clusters = {}
            for order in whale_orders:
                price_range = round(order["price"] / current_price, 2)
                if price_range not in price_clusters:
                    price_clusters[price_range] = 0
                price_clusters[price_range] += 1
            
            max_cluster = max(price_clusters.values()) if price_clusters else 0
            if max_cluster >= 5:
                risk_score += 0.3
                risk_factors.append("whale_price_clustering")
            
            # Factor 4: Distance from current price
            close_whales = [order for order in whale_orders if order["price_distance"] < 0.02]  # Within 2%
            if len(close_whales) >= 3:
                risk_score += 0.2
                risk_factors.append("whales_near_market")
            
            # Calculate final risk metrics
            risk_score = min(1.0, risk_score)
            
            # Risk level classification
            if risk_score >= 0.8:
                risk_level = "extreme"
                market_vulnerability = "critical"
            elif risk_score >= 0.6:
                risk_level = "high"
                market_vulnerability = "high"
            elif risk_score >= 0.4:
                risk_level = "medium"
                market_vulnerability = "moderate"
            elif risk_score >= 0.2:
                risk_level = "low"
                market_vulnerability = "low"
            else:
                risk_level = "minimal"
                market_vulnerability = "minimal"
            
            # Manipulation probability
            manipulation_probability = min(0.9, risk_score * 0.8)
            
            risk_assessment.update({
                "risk_level": risk_level,
                "risk_score": risk_score,
                "risk_factors": risk_factors,
                "market_vulnerability": market_vulnerability,
                "manipulation_probability": manipulation_probability,
                "whale_concentration": whale_count,
                "total_impact": total_impact,
                "close_whale_count": len(close_whales) if 'close_whales' in locals() else 0
            })
            
            return risk_assessment
            
        except Exception as e:
            return {
                "risk_level": "unknown",
                "risk_score": 0.0,
                "error": str(e)
            }
    def _calculate_orderbook_trading_levels(self, bid_levels: List[Dict], ask_levels: List[Dict],
                                      current_price: float, imbalance_analysis: Dict[str, Any],
                                      spread_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """🎯 Calculate CORRECTED Entry/TP/SL from Orderbook analysis với TP targets chính xác"""
        try:
            print(f"        🎯 Calculating CORRECTED Orderbook Trading Levels...")

            # ✅ Extract orderbook metrics
            primary_imbalance = imbalance_analysis.get("primary_imbalance", 0)
            trading_pressure = imbalance_analysis.get("trading_pressure", "balanced")
            spread_quality = spread_analysis.get("spread_quality", "unknown")
            bid_ask_ratio = imbalance_analysis.get("bid_ask_ratio", 1.0)

            # ✅ FIX: Initialize spread_factor early to prevent UnboundLocalError
            spread_factor = max(0.015, spread_analysis.get("relative_spread_pct", 0.05) / 100)
            
            # ✅ ENHANCED SIGNAL DETERMINATION with more flexible thresholds
            if primary_imbalance > 0.15:  # Strong buying pressure (reduced from 0.20)
                signal_type = "BUY"
                confidence_base = 0.85
            elif primary_imbalance < -0.15:  # Strong selling pressure (reduced from -0.20)
                signal_type = "SELL"
                confidence_base = 0.85
            elif abs(primary_imbalance) > 0.08:  # Moderate imbalance (reduced from 0.10)
                signal_type = "BUY" if primary_imbalance > 0 else "SELL"
                confidence_base = 0.70
            elif abs(primary_imbalance) > 0.05:  # Weak but valid imbalance (new tier)
                signal_type = "BUY" if primary_imbalance > 0 else "SELL"
                confidence_base = 0.55
            else:
                # Enhanced backup signal generation
                if bid_ask_ratio > 1.20:  # Reduced from 1.25
                    signal_type = "BUY"
                    confidence_base = 0.50
                elif bid_ask_ratio < 0.85:  # Increased from 0.80
                    signal_type = "SELL"
                    confidence_base = 0.50
                elif bid_ask_ratio > 1.10:  # Additional tier
                    signal_type = "BUY"
                    confidence_base = 0.40
                elif bid_ask_ratio < 0.90:  # Additional tier
                    signal_type = "SELL"
                    confidence_base = 0.40
                else:
                    # Final fallback - always generate a signal if we have orderbook data
                    if len(bid_levels) > 0 and len(ask_levels) > 0:
                        # Use spread quality as signal direction
                        spread_quality = spread_analysis.get("spread_quality", "unknown")
                        if spread_quality in ["excellent", "good"]:
                            # Good spread - use slight imbalance bias
                            signal_type = "BUY" if primary_imbalance >= 0 else "SELL"
                            confidence_base = 0.35
                            print(f"        🔧 Fallback signal from spread quality: {signal_type}")
                        else:
                            # Default to BUY with minimal confidence
                            signal_type = "BUY"
                            confidence_base = 0.30
                            print(f"        🔧 Final fallback signal: {signal_type}")
                    else:
                        return {"has_trading_levels": False, "reason": "insufficient_orderbook_data"}
            
            print(f"        📊 Enhanced Orderbook signal: {signal_type} (imbalance: {primary_imbalance:+.3f}, B/A ratio: {bid_ask_ratio:.2f})")
            
            # ✅ CALCULATE ACCURATE ENTRY PRICE
            if signal_type == "BUY":
                # Slightly below current for better fill, but close to market
                entry_price = current_price * 0.9998  # 0.02% discount
            else:
                # Slightly above current for SELL
                entry_price = current_price * 1.0002  # 0.02% premium
            
            # ✅ ENHANCED TP/SL CALCULATION using orderbook structure
            significant_bids = self._find_significant_orderbook_levels(bid_levels, current_price, "bid")
            significant_asks = self._find_significant_orderbook_levels(ask_levels, current_price, "ask")
            
            if signal_type == "BUY":
                # ✅ CALCULATE BUY TP LEVELS - Going UP from entry
                resistance_levels = [level for level in significant_asks if level["price"] > entry_price]
                
                if len(resistance_levels) >= 2:
                    # Use actual resistance levels
                    first_resistance = resistance_levels[0]["price"]
                    second_resistance = resistance_levels[1]["price"] if len(resistance_levels) > 1 else first_resistance * 1.02
                    
                    # Progressive targets going UP
                    tp1 = entry_price + ((first_resistance - entry_price) * 0.6)   # 60% to first resistance
                    tp2 = entry_price + ((second_resistance - entry_price) * 0.8)  # 80% to second resistance
                    tp3 = second_resistance * 1.01  # Slightly above second resistance
                    
                else:
                    # Calculate percentage-based targets going UP
                    # spread_factor already initialized above

                    # Base targets: 2%, 4%, 6% for BUY
                    tp1_pct = 0.02 + spread_factor  # 2% + spread factor
                    tp2_pct = 0.04 + spread_factor  # 4% + spread factor
                    tp3_pct = 0.06 + spread_factor  # 6% + spread factor
                    
                    # Apply imbalance boost
                    imbalance_boost = max(0, primary_imbalance) * 0.5  # Max 50% boost from positive imbalance
                    tp1_pct += imbalance_boost
                    tp2_pct += imbalance_boost
                    tp3_pct += imbalance_boost
                    
                    # Cap at reasonable levels
                    tp1_pct = min(0.08, tp1_pct)  # Max 8%
                    tp2_pct = min(0.12, tp2_pct)  # Max 12%
                    tp3_pct = min(0.15, tp3_pct)  # Max 15%
                    
                    tp1 = entry_price * (1 + tp1_pct)
                    tp2 = entry_price * (1 + tp2_pct)
                    tp3 = entry_price * (1 + tp3_pct)
                
                # ✅ ENSURE TP1 < TP2 < TP3 for BUY (increasing prices)
                if tp1 >= tp2:
                    tp2 = tp1 * 1.02  # Force 2% above TP1
                if tp2 >= tp3:
                    tp3 = tp2 * 1.02  # Force 2% above TP2
                
                # ✅ CALCULATE STOP LOSS for BUY - Going DOWN from entry
                support_levels = [level for level in significant_bids if level["price"] < entry_price]
                
                if support_levels:
                    strongest_support = max(support_levels, key=lambda x: x["strength"])
                    stop_loss = strongest_support["price"] * 0.995  # 0.5% below support
                else:
                    # Conservative percentage-based stop
                    sl_pct = max(0.015, spread_factor * 3)  # Min 1.5%, based on spread
                    sl_pct = min(0.04, sl_pct)  # Max 4%
                    stop_loss = entry_price * (1 - sl_pct)
            
            else:  # SELL signal
                # ✅ CALCULATE SELL TP LEVELS - Going DOWN from entry
                support_levels = [level for level in significant_bids if level["price"] < entry_price]
                
                if len(support_levels) >= 2:
                    # Use actual support levels
                    first_support = support_levels[0]["price"]
                    second_support = support_levels[1]["price"] if len(support_levels) > 1 else first_support * 0.98
                    
                    # Progressive targets going DOWN
                    tp1 = entry_price - ((entry_price - first_support) * 0.6)   # 60% to first support
                    tp2 = entry_price - ((entry_price - second_support) * 0.8)  # 80% to second support
                    tp3 = second_support * 0.99  # Slightly below second support
                    
                else:
                    # Calculate percentage-based targets going DOWN
                    # spread_factor already initialized above

                    # Base targets: 2%, 4%, 6% for SELL
                    tp1_pct = 0.02 + spread_factor
                    tp2_pct = 0.04 + spread_factor
                    tp3_pct = 0.06 + spread_factor
                    
                    # Apply imbalance boost
                    imbalance_boost = max(0, abs(primary_imbalance)) * 0.5
                    tp1_pct += imbalance_boost
                    tp2_pct += imbalance_boost
                    tp3_pct += imbalance_boost
                    
                    # Cap at reasonable levels
                    tp1_pct = min(0.08, tp1_pct)
                    tp2_pct = min(0.12, tp2_pct)
                    tp3_pct = min(0.15, tp3_pct)
                    
                    tp1 = entry_price * (1 - tp1_pct)
                    tp2 = entry_price * (1 - tp2_pct)
                    tp3 = entry_price * (1 - tp3_pct)
                
                # ✅ ENSURE TP1 > TP2 > TP3 for SELL (decreasing prices)
                if tp1 <= tp2:
                    tp2 = tp1 * 0.98  # Force 2% below TP1
                if tp2 <= tp3:
                    tp3 = tp2 * 0.98  # Force 2% below TP2
                
                # ✅ CALCULATE STOP LOSS for SELL - Going UP from entry
                resistance_levels = [level for level in significant_asks if level["price"] > entry_price]
                
                if resistance_levels:
                    strongest_resistance = max(resistance_levels, key=lambda x: x["strength"])
                    stop_loss = strongest_resistance["price"] * 1.005  # 0.5% above resistance
                else:
                    sl_pct = max(0.015, spread_factor * 3)
                    sl_pct = min(0.04, sl_pct)
                    stop_loss = entry_price * (1 + sl_pct)
            
            # ✅ FINAL VALIDATION - Ensure all values are positive and logical
            if signal_type == "BUY":
                # For BUY: entry < tp1 < tp2 < tp3 and stop_loss < entry
                if not (stop_loss < entry_price < tp1 < tp2 < tp3):
                    print(f"        🔧 Fixing BUY signal logic...")
                    tp1 = entry_price * 1.025  # 2.5%
                    tp2 = entry_price * 1.045  # 4.5%
                    tp3 = entry_price * 1.065  # 6.5%
                    stop_loss = entry_price * 0.975  # -2.5%
            else:
                # For SELL: stop_loss > entry > tp1 > tp2 > tp3
                if not (tp3 < tp2 < tp1 < entry_price < stop_loss):
                    print(f"        🔧 Fixing SELL signal logic...")
                    tp1 = entry_price * 0.975  # -2.5%
                    tp2 = entry_price * 0.955  # -4.5%
                    tp3 = entry_price * 0.935  # -6.5%
                    stop_loss = entry_price * 1.025  # +2.5%
            
            # ✅ CALCULATE RISK/REWARD and validate minimum 2:1
            risk = abs(entry_price - stop_loss)
            reward = abs(tp3 - entry_price)  # Use TP3 as main target
            risk_reward_ratio = reward / risk if risk > 0 else 0
            
            # ✅ ENSURE MINIMUM 2:1 R/R
            if risk_reward_ratio < 2.0:
                print(f"        📊 Adjusting TP3 for better R/R (current: {risk_reward_ratio:.2f})")
                if signal_type == "BUY":
                    tp3 = entry_price + (risk * 2.5)  # Force 2.5:1 R/R
                else:
                    tp3 = entry_price - (risk * 2.5)  # Force 2.5:1 R/R
                
                # Recalculate
                reward = abs(tp3 - entry_price)
                risk_reward_ratio = reward / risk if risk > 0 else 0
            
            # ✅ CALCULATE CONFIDENCE
            spread_confidence = {"excellent": 0.9, "good": 0.8, "fair": 0.6, "poor": 0.4}.get(spread_quality, 0.5)
            imbalance_confidence = min(1.0, abs(primary_imbalance) * 4)
            levels_confidence = min(1.0, (len(significant_bids) + len(significant_asks)) / 8)
            
            overall_confidence = (spread_confidence * 0.4 + imbalance_confidence * 0.4 + levels_confidence * 0.2) * confidence_base
            overall_confidence = min(0.95, max(0.3, overall_confidence))
            
            # ✅ BUILD COMPREHENSIVE TRADING LEVELS
            trading_levels = {
                "has_trading_levels": True,
                "signal_type": signal_type,
                "entry_price": float(entry_price),
                "take_profit": float(tp3),  # Primary TP is TP3
                "stop_loss": float(stop_loss),
                "risk_reward_ratio": float(risk_reward_ratio),
                
                # ✅ CORRECTED TP LEVELS with proper ordering
                "tp_levels": {
                    "tp1": float(tp1),
                    "tp2": float(tp2),
                    "tp3": float(tp3),
                    "tp4": float(tp3 * 1.02 if signal_type == "BUY" else tp3 * 0.98),  # Extended target
                    "primary_tp": float(tp3)
                },
                
                # Analysis details
                "orderbook_analysis": {
                    "primary_imbalance": primary_imbalance,
                    "trading_pressure": trading_pressure,
                    "spread_quality": spread_quality,
                    "bid_ask_ratio": bid_ask_ratio,
                    "significant_levels": len(significant_bids) + len(significant_asks),
                    "support_levels": len(significant_bids),
                    "resistance_levels": len(significant_asks),
                    "signal_confidence": overall_confidence
                },
                
                "calculation_methods": {
                    "entry_method": "orderbook_enhanced_positioning",
                    "tp_method": "progressive_orderbook_levels_corrected",
                    "sl_method": "orderbook_invalidation_enhanced",
                    "target_method": "orderbook_level_projection_enhanced"
                },
                
                "trading_rationale": {
                    "entry_reason": f"Enhanced {signal_type} setup: {primary_imbalance:+.1%} imbalance, {bid_ask_ratio:.2f} B/A ratio",
                    "tp_reason": f"Progressive targets with {risk_reward_ratio:.1f}:1 R/R",
                    "sl_reason": f"Orderbook invalidation at {abs((stop_loss/entry_price - 1)*100):.1f}%",
                    "confidence_reason": f"Strong orderbook: {spread_quality} spread, {overall_confidence:.1%} confidence"
                }
            }
            
            # ✅ COMPREHENSIVE LOGGING
            print(f"        ✅ CORRECTED Orderbook Trading Levels:")
            print(f"          Signal: {signal_type}")
            print(f"          Entry: {entry_price:.8f}")
            if signal_type == "BUY":
                print(f"          TP1: {tp1:.8f} (+{((tp1/entry_price-1)*100):.1f}%)")
                print(f"          TP2: {tp2:.8f} (+{((tp2/entry_price-1)*100):.1f}%)")
                print(f"          TP3: {tp3:.8f} (+{((tp3/entry_price-1)*100):.1f}%)")
                print(f"          SL:  {stop_loss:.8f} ({((stop_loss/entry_price-1)*100):.1f}%)")
            else:
                print(f"          TP1: {tp1:.8f} (+{((entry_price-tp1)/entry_price*100):.1f}%)")
                print(f"          TP2: {tp2:.8f} (+{((entry_price-tp2)/entry_price*100):.1f}%)")
                print(f"          TP3: {tp3:.8f} (+{((entry_price-tp3)/entry_price*100):.1f}%)")
                print(f"          SL:  {stop_loss:.8f} (-{((stop_loss-entry_price)/entry_price*100):.1f}%)")
            print(f"          R/R: {risk_reward_ratio:.2f}")
            print(f"          Confidence: {overall_confidence:.1%}")
            
            return trading_levels
            
        except Exception as e:
            print(f"        ❌ Error calculating CORRECTED Orderbook trading levels: {e}")
            
            # ✅ EMERGENCY CORRECTED FALLBACK
            try:
                signal_type = "BUY"  # Default
                entry_price = current_price * 0.999
                
                if signal_type == "BUY":
                    tp1 = entry_price * 1.02   # 2%
                    tp2 = entry_price * 1.04   # 4%
                    tp3 = entry_price * 1.06   # 6%
                    stop_loss = entry_price * 0.97  # -3%
                else:
                    tp1 = entry_price * 0.98   # -2%
                    tp2 = entry_price * 0.96   # -4%
                    tp3 = entry_price * 0.94   # -6%
                    stop_loss = entry_price * 1.03  # +3%
                
                risk = abs(entry_price - stop_loss)
                reward = abs(tp3 - entry_price)
                risk_reward_ratio = reward / risk if risk > 0 else 2.0
                
                return {
                    "has_trading_levels": True,
                    "signal_type": signal_type,
                    "entry_price": float(entry_price),
                    "take_profit": float(tp3),
                    "stop_loss": float(stop_loss),
                    "risk_reward_ratio": float(risk_reward_ratio),
                    "tp_levels": {
                        "tp1": float(tp1),
                        "tp2": float(tp2),
                        "tp3": float(tp3),
                        "primary_tp": float(tp3)
                    },
                    "emergency_setup": True,
                    "calculation_method": "emergency_corrected_orderbook",
                    "error": str(e)
                }
                
            except Exception as emergency_error:
                return {"has_trading_levels": False, "error": str(emergency_error)}

    def _calculate_corrected_tp_levels(self, signal_type: str, entry_price: float, take_profit: float) -> Dict[str, float]:
        """🎯 Calculate CORRECTED TP levels with proper ordering for SELL signals"""
        try:
            print(f"        🎯 Calculating CORRECTED TP levels for {signal_type} signal...")
            
            if signal_type == "BUY":
                # For BUY: TP1 < TP2 < TP3 (increasing prices)
                tp1 = entry_price + ((take_profit - entry_price) * 0.4)  # 40% of target
                tp2 = entry_price + ((take_profit - entry_price) * 0.7)  # 70% of target  
                tp3 = take_profit                                        # 100% of target
                
            else:  # SELL signal
                # For SELL: TP1 > TP2 > TP3 (decreasing prices, but SMALLEST profit percentage first)
                profit_range = entry_price - take_profit  # Total profit range
                
                # ✅ FIX: Progressive targets with decreasing prices but increasing profit %
                tp1 = entry_price - (profit_range * 0.4)  # 40% down = smallest profit first
                tp2 = entry_price - (profit_range * 0.7)  # 70% down = medium profit  
                tp3 = take_profit                          # 100% down = largest profit
                
                # ✅ ENSURE: TP1 > TP2 > TP3 for SELL (decreasing prices)
                # ✅ VALIDATE: All TPs must be positive and below entry
                if tp1 <= 0 or tp2 <= 0 or tp3 <= 0:
                    print(f"        ⚠️ Negative TP detected, applying conservative targets...")
                    # Conservative targets to avoid negative values
                    tp1 = entry_price * 0.99   # 1% down
                    tp2 = entry_price * 0.97   # 3% down
                    tp3 = entry_price * 0.95   # 5% down
                
                # ✅ VALIDATE: Ensure proper ordering for SELL
                if tp1 <= tp2 or tp2 <= tp3:
                    print(f"        🔧 Fixing TP ordering for SELL signal...")
                    tp1 = max(tp1, tp2 + 0.001, tp3 + 0.002)  # Ensure TP1 > TP2 > TP3
                    tp2 = max(tp2, tp3 + 0.001)                # Ensure TP2 > TP3
            
            # ✅ FINAL VALIDATION
            if signal_type == "SELL":
                # Validate: TP1 > TP2 > TP3 and all positive
                if not (tp1 > tp2 > tp3 > 0):
                    print(f"        🚨 Emergency TP fix for SELL signal...")
                    tp1 = entry_price * 0.995  # 0.5% down
                    tp2 = entry_price * 0.985  # 1.5% down  
                    tp3 = entry_price * 0.975  # 2.5% down
            
            # Calculate profit percentages for display
            if signal_type == "BUY":
                tp1_pct = ((tp1 - entry_price) / entry_price * 100)
                tp2_pct = ((tp2 - entry_price) / entry_price * 100) 
                tp3_pct = ((tp3 - entry_price) / entry_price * 100)
            else:
                tp1_pct = ((entry_price - tp1) / entry_price * 100)
                tp2_pct = ((entry_price - tp2) / entry_price * 100)
                tp3_pct = ((entry_price - tp3) / entry_price * 100)
            
            print(f"        ✅ CORRECTED TP Levels for {signal_type}:")
            print(f"          TP1: {tp1:.8f} ({tp1_pct:+.1f}%)")
            print(f"          TP2: {tp2:.8f} ({tp2_pct:+.1f}%)")
            print(f"          TP3: {tp3:.8f} ({tp3_pct:+.1f}%)")
            
            return {
                "tp1": float(tp1),
                "tp2": float(tp2), 
                "tp3": float(tp3),
                "tp4": float(tp3 * 0.98 if signal_type == "SELL" else tp3 * 1.02),  # Extended target
                "primary_tp": float(tp3)
            }
            
        except Exception as e:
            print(f"        ❌ Error calculating corrected TP levels: {e}")
            # Emergency fallback
            if signal_type == "SELL":
                return {
                    "tp1": float(entry_price * 0.995),
                    "tp2": float(entry_price * 0.985),
                    "tp3": float(entry_price * 0.975),
                    "primary_tp": float(entry_price * 0.975)
                }
            else:
                return {
                    "tp1": float(entry_price * 1.015),
                    "tp2": float(entry_price * 1.025),
                    "tp3": float(entry_price * 1.035),
                    "primary_tp": float(entry_price * 1.035)
                }

    def _find_significant_orderbook_levels(self, levels: List[Dict], current_price: float, side: str) -> List[Dict]:
        """🔍 ENHANCED significant support/resistance levels detection"""
        try:
            if not levels:
                return []
            
            significant_levels = []
            
            # Calculate enhanced volume statistics
            # ✅ FIX: Support both 'quantity' and 'volume' field names
            volumes = []
            for level in levels[:20]:  # Use top 20 levels
                volume = level.get("quantity", level.get("volume", 0))
                volumes.append(volume)

            if not volumes:
                return []
            
            mean_volume = np.mean(volumes)
            std_volume = np.std(volumes)
            median_volume = np.median(volumes)
            
            # Dynamic significance threshold
            significance_threshold = max(
                mean_volume + (1.2 * std_volume),
                median_volume * 2.0,
                volumes[0] * 0.5  # At least 50% of best level
            )
            
            for i, level in enumerate(levels[:15]):  # Check top 15 levels
                # ✅ FIX: Support both 'quantity' and 'volume' field names
                volume = level.get("quantity", level.get("volume", 0))
                price = level["price"]
                
                # Enhanced significance criteria
                if volume >= significance_threshold:
                    # Calculate distance from current price
                    distance = abs(price - current_price) / current_price
                    
                    # Consider levels within 8% of current price
                    if distance <= 0.08:
                        # Enhanced strength calculation
                        volume_strength = volume / mean_volume
                        position_strength = 1 / (1 + i * 0.1)  # Earlier positions = stronger
                        proximity_strength = 1 / (1 + distance * 15)  # Closer = stronger
                        
                        # Combined strength score
                        overall_strength = (volume_strength * 0.5) + (position_strength * 0.3) + (proximity_strength * 0.2)
                        
                        # Quality score based on multiple factors
                        quality_factors = {
                            "volume_dominance": volume / max(volumes),
                            "proximity_to_market": 1 - distance,
                            "orderbook_position": 1 - (i / 15),
                            "relative_size": volume / significance_threshold
                        }
                        
                        quality_score = sum(quality_factors.values()) / len(quality_factors)
                        
                        significant_levels.append({
                            "price": price,
                            "volume": volume,
                            "strength": overall_strength,
                            "quality": quality_score,
                            "distance": distance,
                            "position": i,
                            "side": side,
                            "significance_ratio": volume / significance_threshold,
                            "quality_factors": quality_factors
                        })
            
            # ✅ FIX: Ensure we always return some levels if available
            if not significant_levels and len(levels) > 0:
                # Fallback: Use top levels with relaxed criteria
                print(f"        🔧 No levels met strict criteria, using fallback selection...")
                for i, level in enumerate(levels[:min(3, len(levels))]):
                    volume = level.get("quantity", level.get("volume", 0))
                    price = level["price"]
                    distance = abs(price - current_price) / current_price

                    # Relaxed criteria for fallback
                    if distance <= 0.15:  # Within 15% of current price
                        volume_strength = volume / mean_volume if mean_volume > 0 else 1.0
                        position_strength = 1 / (1 + i * 0.1)
                        proximity_strength = 1 / (1 + distance * 10)

                        overall_strength = (volume_strength * 0.5) + (position_strength * 0.3) + (proximity_strength * 0.2)

                        significant_levels.append({
                            "price": price,
                            "volume": volume,
                            "strength": overall_strength,
                            "quality": 0.5,  # Default quality for fallback
                            "distance": distance,
                            "position": i,
                            "side": side,
                            "significance_ratio": volume / max(volumes) if max(volumes) > 0 else 1.0,
                            "fallback": True
                        })

            # Sort by combined strength and quality
            significant_levels.sort(key=lambda x: x["strength"] * x["quality"], reverse=True)

            # Return top 6 most significant levels
            top_levels = significant_levels[:6]

            # ✅ FIX: Only print if we found levels (avoid "Found 0" messages)
            if len(top_levels) > 0:
                print(f"        🔍 Found {len(top_levels)} significant {side} levels from {len(levels)} total")
                for i, level in enumerate(top_levels[:3]):
                    fallback_indicator = " (fallback)" if level.get("fallback", False) else ""
                    print(f"          {i+1}. {level['price']:.8f} (vol: {level['volume']:.0f}, str: {level['strength']:.2f}){fallback_indicator}")
            else:
                print(f"        ⚠️ No significant {side} levels found from {len(levels)} total levels")

            return top_levels
            
        except Exception as e:
            print(f"        ❌ Error finding significant orderbook levels: {e}")
            return []